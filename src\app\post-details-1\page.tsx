"use client";
import { LENS_CONTRACT_ABI, LENS_CONTRACT_ADDRESS } from "@/const/contracts";
import { CommentOnMomokaMutation, useProfilesQuery } from "@/graphql/generated";
import {
  AccountsBulkQuery,
  AccountsOrderBy,
  CreatePostMutation,
  Follower,
  FollowersOrderBy,
  FollowingOrderBy,
  MainContentFocus,
  PageSize,
  PostReactionType,
  PostReferenceType,
  PostReportReason,
  PostType,
  PostVisibilityFilter,
  useAccountQuery,
  useAccountsBulkQuery,
  useAccountsQuery,
  useFollowersQuery,
  useFollowingQuery,
  useFullAccountQuery,
  usePostQuery,
  usePostReferencesQuery,
  usePostsQuery,
  useReportPostMutation,
  useTransactionStatusQuery,
} from "@/graphql/test/generated";
import useAuth from "@/hook";
import { useBookMark } from "@/lib/useBookMark";
import { useComment } from "@/lib/useCommen";
import { useCreatePost } from "@/lib/useCreatePost";
import { useDeletePost } from "@/lib/useDeletePost";
// import { useCreatePost } from "@/lib/useCreatePost";
import { useFollow } from "@/lib/useFollow";
import { useReaction } from "@/lib/useReaction";
import { useUnFollow } from "@/lib/useUnfollow";
import { useUpdateFeed } from "@/lib/useUpdateFeed";
import {
  getAllPosts,
  getAllPostsOptimized,
  getPaginatedUsersByCategoryPosts,
  getPostsByPostIdUserId,
  getPostsByUserSorted,
} from "@/services/postService";
import { fetchHelpFAQGroup, getAboutContent } from "@/services/remoteConfigService";
import {
  createService,
  CustomizationInput,
  deleteCustomization,
  getCustomisations,
  getServiceById,
  getServicesByCategory,
  getServicesByUserId,
  Service,
  updateCustomizations,
} from "@/services/serviceService";
import { MediaRenderer, Web3Button } from "@thirdweb-dev/react";
import { collection, getDocs, Timestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import { initFirebase } from "../../../firebaseConfig";
import SignInButton from "@/components/SignInButton";
import { useDisconnect } from "wagmi";
import { FollowerManager } from "@/services/followServices";
import { AddtoOrderState, clean, CreateOrder_V2, GetOrderDetailsByUserId, SendInvoiceRequest, testOrderDecline, UpdateActivityLog, UpdateOrderDueDate, UpdateOrderStripeDetails, UpdateUserAccountId } from "@/services/ordersServices";
import { NotificationEvents, NotificationManager } from "@/services/notificationService";
import { deleteUserDetails, getAllUsers, GetSidebarCount, sendResetPassword, updatePostDeleteFlag } from "@/services/usersServices";
import { getLensProfilesById_V2 } from "@/services/lensService";
import { ChatManager, Messages } from "@/services/chatService";
import { EventSearchBy, FilterSearchManager, PostSearchBy, ProfileSearchBy, PublishingDateFilter, ServiceSearchBy } from "@/services/filtersServices";
import { log } from "console";
import { OrderStatusType } from "@/lib/constant";
import { MailServiceManager } from "@/services/MailService";
import { getAuth } from "firebase/auth";

export default function Home() {
  // ########################################## usePublicationQuery ##########################################✅
  // const { isLoading, data, error } = usePublicationQuery({
  //   request: {
  //     // post:"39130437942447979329874165024976872993139868756996365088967062590380725348778"
  //     forId: "0x89dc-0x1796-DA-dc1a7342",
  //   },
  // });
  const { disconnect } = useDisconnect();

  const { isLoading, data, error } = usePostQuery(
    {
      request: {
        post: "3dhmc65p9nft8mrbny7",
        // forId: "0x89dc-0x1796-DA-dc1a7342",
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // console.log({ data });

  // ########################################## useFollowersQuery ##########################################✅
  // const { data: followers, isLoading: followers_loading } = useFollowersQuery({
  //   request: {
  //     of: otherUserID.otherUserID,
  //     orderBy: ProfilesOrderBy.Default,
  //     limit: LimitType.Fifty,
  //     cursor: currentFollowersCursor,
  //   },
  // });
  const { data: followers, isLoading: followersLoading } = useFollowersQuery(
    {
      request: {
        account: "0xD8D785856903e1ef47DbAdC40d076f6ef1aC537F", // address
        pageSize: PageSize.Ten,
        orderBy: FollowersOrderBy.Desc,
        // cursor:null
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // console.log({ followers });

  // ########################################## useFollowingQuery ##########################################✅
  // - 4. useFollowingQuery
  //  const { data: following, isLoading: following_loading } = useFollowingQuery({
  //   request: {
  //     for: otherUserID.otherUserID,
  //     orderBy: ProfilesOrderBy.Default,
  //     limit: LimitType.Fifty,
  //     cursor: currentFollowingCursor,
  //   },
  // });
  const { data: followings, isLoading: followingsLoading } = useFollowingQuery(
    {
      request: {
        account: "0xD8D785856903e1ef47DbAdC40d076f6ef1aC537F", // address
        pageSize: PageSize.Ten,
        orderBy: FollowingOrderBy.Desc,
        // cursor:null
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // console.log({ followings });

  // ########################################## usePublicationsQuery(comments) #################✅
  // - 6. usePublicationsQuery (comments)
  // const {
  //   isLoading: commentsLoading,
  //   data: comments,
  //   error: commentsError,
  // } = usePublicationsQuery({
  //   request: {
  //     where: {
  //       commentOn: {
  //         id: "0x05-0x27b7-DA-dccf390c",
  //         hiddenComments: HiddenCommentsType.Hide,
  //         ranking: {
  //           filter: CommentRankingFilterType.Relevant,
  //         },
  //       },
  //     },
  //     limit: LimitType.TwentyFive,
  //     cursor: currentCursor,
  //   },
  // });

  const {
    isLoading: commentsLoading,
    data: comments,
    error: commentsError,
  } = usePostReferencesQuery(
    {
      request: {
        referencedPost: "kjtsr6z7675p9nrn2t", // post id,
        referenceTypes: [PostReferenceType.CommentOn],
        // cursor:null,
        pageSize: PageSize.Fifty,
        visibilityFilter: PostVisibilityFilter.Visible,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // ########################################## usePublicationsQuery (posts) ############✅
  // - 7. usePublicationsQuery (posts)
  // const {
  //   isLoading: isLoadingPublications,
  //   data: publicationsData,
  //   error: publicationsError,
  // } = usePublicationsQuery({
  //   request: {
  //     where: {
  //       from: profilesIds,
  //       publicationTypes: [
  //         PublicationType.Post,
  //         // PublicationType.Quote,
  //       ],

  //       metadata: {
  //         mainContentFocus: [
  //           PublicationMetadataMainFocusType.Image,
  //           PublicationMetadataMainFocusType.Video,
  //         ],
  //       },
  //     },
  //     limit: LimitType.Fifty,
  //     cursor: currentCursor,
  //   },
  // });

  const {
    isLoading: postsLoading,
    data: posts,
    error: postsError,
  } = usePostsQuery(
    {
      request: {
        filter: {
          authors: ["0xb492b704b59f7657F677D617411EA86669b59D71"], // joanakawaharalino
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        // cursor:null
        pageSize: PageSize.Ten,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // console.log({ posts });

  // ########################################## useProfileQuery ##########################################✅
  // - 2. useProfileQuery
  //  const { data: profileData } = useProfileQuery({
  //     request: {
  //       forProfileId: "0x0215d1",
  //     },
  //   });

  const { data: profileData } = useAccountQuery(
    {
      request: {
        address: "0xb492b704b59f7657F677D617411EA86669b59D71",
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // console.log({ profileData });

  // 🔥🔥🔥🔥<<<<<<<<<<<<<<<<<<fetchLensProfilesByCategory>>>>>>>>>>>>>>>>>>🔥🔥🔥🔥 fix

  // ########################################## useProfilesQuery ##########################################
  // - 1. useProfilesQuery
  //   const { data: profileDataByLensId } = useProfilesQuery({
  //     request: { where: { handles: profiles } },
  //   });

  const address: string = "0xb492b704b59f7657F677D617411EA86669b59D71";
  const { data: profileDataByLensId } = useAccountsBulkQuery(
    {
      request: {
        //  addresses:[address],
        // legacyProfileIds:["joanakawaharalino"]
        usernames: [
          {
            localName: "joanakawaharalino",
          },
          {
            localName: "divyam001",
          },
        ],
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!address,
    }
  );
  type Status = "follow" | "unfollow";
  const [CurrentStatus, setCurrentStatus] = useState<Status>("follow");
  const [Txn_id, setTxn_id] = useState<string | null>(null);

  // console.log("-->", { profileDataByLensId });

  // profileDataByLensId?.accountsBulk?.map((current:AccountsBulkQuery["accountsBulk"][0])=>{
  //   current.
  // })

  // ########################################## useLensTransactionStatusQuery ##########################################
  // const { data: transactionData, refetch: refetchTransactionStatus } =
  //     useLensTransactionStatusQuery({
  //       request: { forTxId: Txn_id },
  //     });
  const { data: transactionData, refetch: refetchTransactionStatus } = useTransactionStatusQuery(
    {
      request: {
        txHash: Txn_id,
      },
    },
    {
      enabled: !!Txn_id,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (!Txn_id) return;
    // console.log({ Txn_id });

    const POLL_INTERVAL = 1000;
    const MAX_CHECK_COUNT = 15;
    let checkCnt = 0;

    const intervalId = setInterval(async () => {
      if (checkCnt > MAX_CHECK_COUNT) {
        alert(` timeout `);
        clearInterval(intervalId);
      }
      if (
        transactionData?.transactionStatus?.__typename === "FinishedTransactionStatus" ||
        transactionData?.transactionStatus?.__typename === "FailedTransactionStatus"
      ) {
        alert(`comment added successfully ✅`);
        clearInterval(intervalId);
      } else {
        checkCnt++;
        await refetchTransactionStatus();
      }
    }, POLL_INTERVAL);

    return () => clearInterval(intervalId); // Cleanup on unmount
  }, [Txn_id, transactionData, refetchTransactionStatus]);

  // done
  const addReaction = useReaction();
  const bookmarkResp = useBookMark();
  const { mutateAsync: followUser, isSuccess, data: followData } = useFollow();
  const unfollow = useUnFollow();
  const commentResp = useComment();
  const postResp = useCreatePost();
  const deletePostResp = useDeletePost();
  const feedResp = useUpdateFeed();
  const { mutateAsync: reportPost } = useReportPostMutation();
  // done

  const check = async () => {
    try {
      // sendResetPassword()
      ///////////////////////// 🔥🔥🔥🔥🔥🔥 CREATING SERVICE WITH CUSTOMISATIONS 🔥🔥🔥🔥🔥🔥 /////////////////////////

      // const resp = await FilterSearchManager.getInstance().GetProfileByFilters({
      //   payload:{
      //     filterBy:ProfileSearchBy.PROFILE_NAME,
      //     searchTerm:"",
      //   }
      // })

      // console.log({resp});
      // const resp = await UpdateUserAccountId({
      //   user_id:"xXbxqfSGyodxCtCH3kcwfZBylqD3" , 
      //   stripe_id:"acc_1231231lkn"
      // })

      // brits3
      // x2
      // x 1
  //     const resp = await UpdateActivityLog({
  //     orderId:"7PNUA6L5F6u17e7I87RT",
  // description:"apple",
  // from:"creator",
  // title:OrderStatusType.DELIVERED,
  // loggedInUser:"divyam",
  // sellerName:"aman",
  // userName:"divyam" , // buyer
  // reason:"dummy reason ",        
  //     })
  // const resp = await UpdateActivityLog({
  //   id:"0lES4kHs2gR6olbU98HA",
  //   loggedInUser:"dadds",
  //   sellerName:"seller",
  //   userName:"buyer"
  // })
      
  // testOrderDecline()
  // UpdateOrderDueDate("oQEfOdJgb9McbQ6oIGIw");
  
      // console.log({resp});
      
      
      
      
      
      
      // const formData: Service = {
      //   title: "test",
      //   category: "Art",
      //   // about,
      //   price: "130",
      //   // listPrice,
      //   duration: "9",
      //   description: "dummy service",
      // };
      // //
      // const customizations: CustomizationInput[] = [
      //   {
      //     description: "newnew1",
      //     duration: "22",
      //     price: "99",
      //     title: "titl-4",
      //   },
      // ];
      // const resp = await createService(formData,customizations)
      // console.log({resp});

      // const resp = await getServicesByUserId("lOZOrQcPygM3gAoEiDIxHBU0dgw2");
      // const resp = await deletePost("SVS0FawCM6m2xKTEqNfe");
      // for category
      const resp = await getServicesByCategory("Art",undefined,{
        user_id:["fKuo2tcOUzZa3IAUQ636Ndpzbj02"],
      });
      // for my-feed pass getServicesByCategory("my-feed" , userId);
      console.log({resp});
      // const resp = await getCustomisations();
      // const resp = await getUsersSortedByLastPost("my-feed","W5437xR475cbUigqs5BlqWStrZw1");
      // console.log({resp});

      // const resp = await CommentManager.getInstance().GetCommentsByPostId("0Ei607dONeMn6yM8YmZ9");
      // const resp = await createService(formData , customizations)
      // const resp = await updateCustomizations("5HjuVzoWebbLrq0fXgwd", customizations);
      // const resp = await getServiceById("6fYyO3c90ubjU93ZaHPl");
      // const resp = await getServicesByUserId("Y3f83F4U7uf8K3ZVardNPj3mrxf2");
      // const resp = await deleteCustomization("k13wIT3Q6ckJKxpTzyLH","6Uz0laG9PK9BTYMUoaWO");
      // console.log({resp});
      // const resp = await getAllPostsTest();

      // console.log({ resp });
    } catch (error) {
      console.log({ error });
    }
  };

  const [files, setFiles] = useState<any>([]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (selectedFiles) {
      const newFiles = Array.from(selectedFiles).map((file) => ({
        id: `${file.name}-${file.lastModified}`,
        file,
      }));
      // console.log({ file: newFiles?.[0]?.file });
      setFiles(newFiles?.[0]?.file);

      const resp = await postResp(newFiles?.[0]?.file, "apple mango grapes #app");
      // console.log({ resp });
    }
  };

  const updateLensFeed = async () => {
    // name
    // profile pic
    // location
    // feed

    const name = "divyam0101";
    const location = "bharat";
    const feed = "nothing...12345";
    let image: "";

    // every field is mandatory here if you want

    const resp = await feedResp({
      feed,
      location,
      name,
      // image_url , image ,
      // upload_image
      // image:newFiles?.[0]?.file
    });
  };

  const deleteLensPost = async () => {
    // const resp = deletePostResp("2awm4btg4tm0mne1xzb");

    // console.log({resp});

    const resp = await fetchHelpFAQGroup();

    // console.log({ resp });
  };

  const auth = useAuth();

  const UpdatePosts = async () => {
    // console.log({ auth });

    const { db } = await initFirebase();

    const snapshot = await getDocs(collection(db, "posts"));

    const missingAddedAt = snapshot.docs.filter((doc) => !doc.data().hasOwnProperty("user_id"));
    let arr: any = [];

    missingAddedAt.forEach((docSnap) => {
      const data = docSnap.data();
      arr.push(data);
    });

    // console.log({ arr });
  };
      let startAfterAddedAt:any = null;

  const testingFx = async () => {
    try {
      console.time("✅");

      // const resp = await NotificationManager.getInstance().CreateNotification({
      //   payload:{
      //     src_id:"aldkanksdnalds" ,
      //     dest_id :"a23123sdlasndalskdasaskdasndlknasldk" ,
      //     event:NotificationEvents.REACTION ,
      //     post_id:"asdkasldkasndkla",
      //     post_url:"asdas",
      //   }
      // })

      // const resp = await NotificationManager.getInstance().GetNotificationsByUserId({user_id:"a23123sdlasndalskdasaskdasndlknasldk"});
      // const resp = await NotificationManager.getInstance().UserUnreadNotificationCount({user_id:"a23123sdlasndalskdasaskdasndlknasldk"});

      // console.log({resp});

      // await clean();

      // const resp = await GetOrderDetailsByUserId({userId:"JC3XSXEOUpegE6OKjJwWvLPxVHM2" ,status:"BASKET"});

      // profileId -> whose service its there
      // userProfileId -> who is taking the service

      // const resp = await CreateOrder_V2({
      //   orderData:{
      //     profileId:"SMGxkRxRhENRi5wuZMQf5Iq0F8X2",
      //     serviceId:"5Bkazd363k8h06YeG13i",
      //     status:"BASKET" ,
      //     userProfileId:"JC3XSXEOUpegE6OKjJwWvLPxVHM2",
      //     comment:"ASDLASNDKLASNDKLASD" ,
      //   }
      // })

      // console.log({resp});

      // const resp = await deleteUserDetails({
      //   user_id: "JnBnTapEnxM8pkYbm7bLMvU1mCp1",
      //   comment: "hii",
      // });
      // // // const resp = await FollowerManager.getInstance().GetFollowingsByUserId("YHcuMxBfl1R7J562fUFwIghBqQw2")
      // console.log({ resp });

      // const resp = await getLensProfilesById_V2({
      //   count: 50,
      //   id: "music",
      //   page: 1,
      // });

      // console.log({ auth });

      // 🚀 send message
      // const resp = await ChatManager.getInstance().SendMessage({
      //   senderId: auth.userId,
      //   senderName: auth.userData?.profile_name ?? "",

      //   recipientId: auth.userId === "G4wu9whD2eTLAZVDqEiYkfGXge43" ?
      //   //  "SzOqatsV6OM6ZGcPqUtjQ7vAuG33":
      //    "xXbxqfSGyodxCtCH3kcwfZBylqD3": // danish
      //    "G4wu9whD2eTLAZVDqEiYkfGXge43",
      //   recipientName: auth.userId === "G4wu9whD2eTLAZVDqEiYkfGXge43" ?
      //   // "another_divyam"
      //   "Danish Ansari dev db"
      //   :"DIVYAM_001",

      //   message: "apple mango grapes",
      // });

      // const resp = await ChatManager.getInstance().GetUnreadMessagesCountByChatId({user_id:auth.userId , chatId:"7uCVSwjadGHjopmJzpMH"});

      // const resp = await ChatManager.getInstance().GetUserChatSummaries({user_id:auth.userId});
      // const resp = await CreateOrder_V2({
      //   orderData: {
      //     comment: "✅aaks✅🔴✅dlnnalknsd asdnkl ✅ bhh. 🔴",
      //     profileId: "LJgKQupO0mNUZmXVktZRMBZuGhx1",
      //     selectedCustomizations: ["REPT4Mpy34QKIYE8dpHu"],
      //     serviceId: "BkRWRABY2vdzvXgZegRl",
      //     status: "BASKET",
      //     userProfileId: "xIZQpWZKPyOIUaco6xyMyCmdq3P2",
      //   },
      // });
      // const resp = await GetSidebarCount();
      
      // "Berlin, Germany"
      
      // const resp = await FilterSearchManager.getInstance().GetPostByFilters({
      //   payload:{
      //     filterBy:PostSearchBy.ABOUT_PROJECT ,
      //     searchTerm:"amuzn",
      //     limit:50,
      //     startAfterAddedAt,
      //     filters:{
      //       // category:["Film & Photography","Theatre & Performance","Groups"],
      //       date_of_publishing:PublishingDateFilter.LAST_MONTH
      //     }
      //   }
      // })

      //   const resp = await FilterSearchManager.getInstance().GetServiceByFilters({
      //   payload:{
      //     filterBy:ServiceSearchBy.DESCRIPTION ,
      //     searchTerm:"asdas",
      //     limit:50,
      //     startAfterAddedAt,
      //     filters:{
      //       category:["Film & Photography","Theatre & Performance","Groups"],
      //       date_of_publishing:PublishingDateFilter.LAST_MONTH
      //     }
      //   }
      // })

      // const resp = await FilterSearchManager.getInstance().GetProfileByFilters({
      //   payload:{
      //     filterBy:ProfileSearchBy.PROFILE_NAME ,
      //     searchTerm:"divyam",
      //     limit:50,
      //     startAfterAddedAt
      //   }
      // })      

      

      // startAfterAddedAt = resp?.nextStartAfterAddedAt;
      // const resp = await GetOrderDetailsByUserId({
      //   userId:"YHcuMxBfl1R7J562fUFwIghBqQw2"
      // })

      // console.log({ resp });

      // const resp = await NotificationManager.getInstance().GetNotificationsByUserId({user_id:"YHcuMxBfl1R7J562fUFwIghBqQw2"})
      // console.log({resp});

      // const resp = await getPostsByPostIdUserId({
      //   loggedInUserId:"Y3f83F4U7uf8K3ZVardNPj3mrxf2",
      //   postId:"g76yuTVuyhn1nLSJMO63",
      //   userId:"W5437xR475cbUigqs5BlqWStrZw1", // post user id
      //   category:"Music"
      // })
      // console.log({resp});

      // const resp = await getPaginatedUsersByCategoryPosts({
      //   category: "Art",
      //   // category:"my-feed" ,
      //   pageSize: 10, // ****** dont go above 10 *****
      //   loggedInUserId: "Y3f83F4U7uf8K3ZVardNPj3mrxf2",
      //   filters:{
      //     location:['Manchester, UK',"Camden, London, UK"],
      //     user_id:["JAe9W0LcfyXZ5nlzxtzJhqtF9UN2"]
      //   }
      //   // exclude_user_id:"Opcw9QEdfzLW1do9kPyDSMp4zvv1"

      //   // startAfterUserId:"pW8FQLP5GZeSCOlQvre3Pawysbn1"
      //   // startAfterUserId:"K3aIo2eTjFgz89EHzSKsxDxab2E3"
      // });

      // const resp = await getAllUsers({
      //   user_id:["LicnbFp2IaS6QjiHgzQXCECxjSL2"]
      // })
      // const resp = await GetLastActivityLogStatus({
        // 
      // })

      // console.log({ resp });
      // const posts = await getAllPosts();
      // console.log({posts});

      // const resp = await SendInvoiceRequest({orderId:"VYvprGJMxcrbU2Gcyxh3"});
      

      console.timeEnd("✅");
    } catch (error) {
      console.log({ error });
    }

    //   useEffect(() => {
    //   console.log("✅ useEffect fired");

    //   let stopListening: () => void;

    //   (async () => {
    //     stopListening = await ChatManager.getInstance().GetMessagesBetweenUsers({
    //       userA:
    //         auth.userId === "G4wu9whD2eTLAZVDqEiYkfGXge43"
    //           ? "SzOqatsV6OM6ZGcPqUtjQ7vAuG33"
    //           : "G4wu9whD2eTLAZVDqEiYkfGXge43",
    //       userB: auth.userId,
    //       onUpdate: (messages: Messages[]) => {
    //         console.log("Live messages", messages);

    //         if (messages?.length) {
    //           ChatManager.getInstance().UpdateSeenFlag({
    //             chatId: messages?.[0]?.chatId,
    //             currentUserId: auth?.userId,
    //             msgs: messages,
    //           });
    //         }
    //       },
    //     });
    //   })();

    //   return () => {
    //     if (stopListening) stopListening();
    //   };
    // }, [auth.userId]);

    // console.log({auth});
  };

  const initChat = async () => {
    console.log("init");

    await ChatManager.getInstance().CreateChatIfNotExists({
      fromProfile: auth.userId,
      fromProfileName: auth.userData?.profile_name ?? "",
      toProfile:
        auth.userId === "G4wu9whD2eTLAZVDqEiYkfGXge43"
          ? "xXbxqfSGyodxCtCH3kcwfZBylqD3" // danish
          : "G4wu9whD2eTLAZVDqEiYkfGXge43",
      toProfileName:
        auth.userId === "G4wu9whD2eTLAZVDqEiYkfGXge43" ? "Danish Ansari dev db" : "DIVYAM_001",
    });

    const stop = await ChatManager.getInstance().GetMessagesBetweenUsers({
      userA:
        auth.userId === "G4wu9whD2eTLAZVDqEiYkfGXge43"
          ? // "SzOqatsV6OM6ZGcPqUtjQ7vAuG33":
            "xXbxqfSGyodxCtCH3kcwfZBylqD3" // danish
          : "G4wu9whD2eTLAZVDqEiYkfGXge43",
      userB: auth.userId,
      onUpdate: (messages: Messages[]) => {
        console.log("Live messages", messages);

        if (messages?.length) {
          ChatManager.getInstance().UpdateSeenFlag({
            chatId: messages?.[0]?.chatId,
            currentUserId: auth?.userId,
            msgs: messages,
          });
        }
      },
    });

    // Later when cleaning up:
    stop();
  };

  return (
    <>
      <div onClick={initChat}> init </div>

      <div onClick={testingFx}>testing.....</div>

      <div className="flex">
        <SignInButton />
      </div>
      <div
        onClick={() => {
          disconnect();
        }}
      >
        disconnect
      </div>
      <div onClick={UpdatePosts}>Update post</div>

      <div onClick={deleteLensPost}>delete post</div>

      <div onClick={updateLensFeed}>updateLensFeed</div>

      <div>
        upload
        {/* <input type="file" onChange={handleFileChange} /> */}
      </div>

      <div>hello</div>

      <div>
        <button onClick={check}>check</button>
      </div>

      <div>
        <h1>Report post 1n4wvr7ew09vc7k5544</h1>

        <button
          onClick={async () => {
            // const resp = await reportPost({
            //   request: {
            //     post: "1n4wvr7ew09vc7k5544",
            //     additionalComment: "testing",
            //     reason: PostReportReason.Misleading,
            //   },
            // });
            // console.log({ resp });
          }}
        >
          Report
        </button>
      </div>

      <button
        onClick={async () => {
          const resp: CreatePostMutation = await commentResp(
            "3jf9yvmgcym79neh658",
            "goku 🔥🔥🔥🔥",
            true // momoka
          );
          if (resp?.post?.__typename === "PostResponse") {
            setTxn_id(resp?.post?.hash);
          }
          // console.log({ resp });
        }}
      >
        {" "}
        comment post (https://hey.xyz/posts/3jf9yvmgcym79neh658)
      </button>

      <div className="flex gap-2">
        {/* <Web3Button

              contractAddress={LENS_CONTRACT_ADDRESS}
              contractAbi={LENS_CONTRACT_ABI}
              action={async () => {
                setCurrentStatus("follow");
                await followUser("0x0215d1");
              }}
            >
              Follow User
            </Web3Button> */}
        <div
          onClick={async () => {
            setCurrentStatus("follow");
            await followUser("0xb492b704b59f7657F677D617411EA86669b59D71");
          }}
        >
          Follow User
        </div>

        {/* <Web3Button
              contractAddress={LENS_CONTRACT_ADDRESS}
              contractAbi={LENS_CONTRACT_ABI}
              action={async () => {
                setCurrentStatus("unfollow");
                const resp = await unfollow("0x0215d1");
                setTxn_id(
                  // @ts-ignore
                  resp.txId
                );
              }}
            > */}
        {/* UnFollow User */}
        {/* </Web3Button> */}
        <div
          onClick={async () => {
            setCurrentStatus("unfollow");
            // const resp = await unfollow("0xb492b704b59f7657F677D617411EA86669b59D71");
            // setTxn_id(
            //   // @ts-ignore
            //   resp.txId
            // );
          }}
        >
          UnFollow User
        </div>
      </div>

      <button
        onClick={async () => {
          // const resp = await bookmarkResp("kjtsr6z7675p9nrn2t", true);
        }}
      >
        {" "}
        bookmark post (https://hey.xyz/posts/kjtsr6z7675p9nrn2t)
      </button>

      <h1>post:kjtsr6z7675p9nrn2t</h1>

      <button
        onClick={async () => {
          // const resp = await addReaction("kjtsr6z7675p9nrn2t", PostReactionType.Upvote, "like");
          // console.log({ resp });
        }}
      >
        Like post{" "}
      </button>

      <button
        onClick={async () => {
          // const resp = await addReaction(
          //   "kjtsr6z7675p9nrn2t",
          //   PostReactionType.Downvote,
          //   "dislike"
          // );
          // console.log({ resp });
        }}
      >
        Dislike post{" "}
      </button>

      <h1>--------------------------------------------------- posts</h1>
      {/* 
      {posts?.posts.items.map((currentPost) => {
        return (
          <div>
            {
              // @ts-ignore
              currentPost?.metadata?.image?.item && (
                // @ts-ignore
                <img src={currentPost?.metadata?.image?.item || ""} />
              )
            }
            {"by " + currentPost?.author?.username?.localName}
          </div>
        );
      })}

      <h1>--------------------------------------------------- comments</h1>

      {comments?.postReferences.items?.map((currentComment, index) => {
        return (
          <div>
            <div>
              {
                <div>
                  {currentComment?.__typename === "Post" &&
                    (currentComment?.metadata?.__typename === "ImageMetadata" ||
                      currentComment?.metadata?.__typename === "VideoMetadata") &&
                    currentComment?.metadata?.content +
                      "  " +
                      "by :" +
                      currentComment?.author?.username?.localName}
                </div>
              }
            </div>
          </div>
        );
      })}

      <h1>--------------------------------------------------- Followers</h1>
      {followers?.followers.items.map((currentFollower) => {
        return <div>{currentFollower.follower.username?.localName}</div>;
      })}

      <h1>---------------------------------------------------followings</h1>
      {followings?.following.items.map((currentFollower) => {
        return <div>{currentFollower.following.username?.localName}</div>;
      })} */}

      <div>usePostQuery</div>
      {
        // @ts-ignore
        data?.post?.metadata?.image?.item && <img src={data?.post?.metadata?.image?.item || ""} />
      }
    </>
  );
}
