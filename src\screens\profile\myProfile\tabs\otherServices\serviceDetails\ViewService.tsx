"use client";
import { getServiceById } from "@/services/serviceService";
import { useEffect, useState } from "react";
import { X, Loader } from "react-feather";
import { getCurrencySymbol, initializeCurrency } from "@/services/currencyService";
import LazyMedia from "@/components/LazyMedia";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import Video from "yet-another-react-lightbox/plugins/video";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import "yet-another-react-lightbox/plugins/thumbnails.css";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import RichTextFormatter from "@/components/RichTextFormatter";

interface ViewServiceProps {
  onSelectService: (id: number) => void;
  id: string;
  custamizationId: string;
  currencySymbol?: string;
}

const ViewService = ({
  onSelectService,
  id,
  custamizationId,
  currencySymbol: propCurrencySymbol,
}: ViewServiceProps) => {
  const [services, setServices]: any = useState();
  const [currencySymbol, setCurrencySymbol] = useState(propCurrencySymbol || "£");
  const [isLoading, setIsLoading] = useState(true);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxSlides, setLightboxSlides] = useState<any[]>([]);

  const generateFileUrl = (mediaItem: string | any): string | undefined => {
    // Handle both string URLs and object format
    const postFile = typeof mediaItem === "string" ? mediaItem : mediaItem?.url;

    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) {
      return undefined;
    }

    if (typeof postFile === "string" && postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  useEffect(() => {
    const fetchAllServices = async () => {
      setIsLoading(true);

      try {
        const response = await getServiceById(id);

        if (response.success) {
          setServices(response.service);

          // Get currency from service data if not provided in props
          if (!propCurrencySymbol) {
            const servicesCurrency = initializeCurrency();
            setCurrencySymbol(getCurrencySymbol(servicesCurrency));
          }
        }
      } catch (error) {
        console.error("Error fetching customization details:", error);
      } finally {
        // Add a small delay to ensure loading state is visible
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      }
    };

    fetchAllServices();
  }, [id, propCurrencySymbol]);

  // Helper function to determine if media item is a video
  const isVideoMedia = (mediaItem: any): boolean => {
    if (typeof mediaItem === "string") {
      return mediaItem.includes("videos");
    }
    return mediaItem?.type === "videos";
  };

  // Prepare lightbox slides from media array
  const prepareLightboxSlides = (mediaArray: any[]) => {
    return mediaArray.map((mediaItem: any) => {
      const mediaUrl = generateFileUrl(mediaItem);
      const isVideo = isVideoMedia(mediaItem);

      if (isVideo) {
        return {
          type: "video",
          width: 1280,
          height: 720,
          controls: true,
          muted: false,
          autoPlay: false,
          poster: mediaUrl,
          sources: [
            {
              src: mediaUrl,
              type: "video/mp4",
            },
          ],
        };
      } else {
        return {
          src: mediaUrl,
          width: 1280,
          height: 720,
        };
      }
    });
  };

  // Handle lightbox open
  const handleLightboxOpen = (mediaArray: any[], clickedIndex: number) => {
    const slides = prepareLightboxSlides(mediaArray);
    setLightboxSlides(slides);
    setLightboxIndex(clickedIndex);
    setLightboxOpen(true);
  };

  return (
    <>
      <div>
        <div className="bg-white">
          {/* header */}
          <div className="w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div onClick={() => onSelectService(1)} className="cursor-pointer">
                <X />
              </div>
              <p className="text-titleLabel text-lg font-bold">Customization Details</p>
              <p className="opacity-0"></p>
            </div>
          </div>
          <div className="flex flex-col w-full overflow-scroll gap-3 hide-scroll-custom bg-white h-[calc(100vh-300px)] pt-4 pb-16 px-3">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center h-[60vh]">
                <div className="bg-blue-50 rounded-full p-4 mb-4">
                  <Loader size={48} className="text-primary animate-spin" />
                </div>
                <h2 className="text-xl font-bold mb-2 text-center">
                  Loading Customization Details...
                </h2>
                <p className="text-gray-500 text-center">
                  Please wait while we load the customization information.
                </p>
              </div>
            ) : (
              <>
                {services?.customizations_array?.map((item: any, indexs: any) => (
                  <div key={indexs}>
                    {custamizationId === item.id && (
                      <div className="">
                        <p className="my-3 text-primary text-xl font-bold">{item?.title}</p>
                        <RichTextFormatter
                          text={item?.description || ""}
                          className="mt-2 text-subtitle"
                          preserveWhitespace={true}
                          enableMarkdown={true}
                        />

                        {item?.price && (
                          <div className="mt-2">
                            <p className="text-subtitle">
                              Price: {currencySymbol}
                              {(Number(item.price) / (1 - 0.16)).toFixed(2)}
                            </p>
                          </div>
                        )}

                        <div className="mt-3">
                          {item?.media && item.media.length > 0 ? (
                            <div>
                              {/* <p className="text-primary font-[600] mb-2">
                                Media Files ({item.media.length})
                              </p> */}
                              <div className="grid grid-cols-2 gap-3">
                                {item.media.map((mediaItem: any, mediaIndex: number) => {
                                  const mediaUrl = generateFileUrl(mediaItem);
                                  const isVideo = isVideoMedia(mediaItem);

                                  return (
                                    <div
                                      key={mediaIndex}
                                      className="relative rounded-md overflow-hidden h-36 border border-gray-200"
                                    >
                                      <LazyMedia
                                        src={mediaUrl}
                                        alt={`Media ${mediaIndex}`}
                                        type={isVideo ? "video" : "image"}
                                        className="w-full  object-cover cursor-pointer h-36"
                                        placeholderClassName={
                                          isVideo ? "bg-gray-800" : "bg-gray-100"
                                        }
                                        showPlayIcon={isVideo}
                                        playIconClassName="top-2 right-2"
                                        controls={false}
                                        autoPlay={false}
                                        muted={true}
                                        enableLightbox={true}
                                        onLightboxOpen={() =>
                                          handleLightboxOpen(item.media, mediaIndex)
                                        }
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          ) : (
                            <p className="text-gray-500">No media available</p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {!services?.customizations_array?.some(
                  (item: any) => item.id === custamizationId
                ) && <p className="text-gray-500">No details available for this customization.</p>}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Lightbox with Thumbnails, Zoom, and Navigation */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        index={lightboxIndex}
        slides={lightboxSlides}
        plugins={[Video, Thumbnails, Zoom]}
        carousel={{
          preload: 2,
          finite: true,
          spacing: 0,
          padding: 0,
        }}
        toolbar={{ buttons: ["close"] }}
        controller={{
          closeOnBackdropClick: true,
          touchAction: "none",
        }}
        thumbnails={{
          position: "bottom",
          width: 120,
          height: 80,
          border: 2,
          borderRadius: 4,
          padding: 4,
          gap: 16,
          imageFit: "cover",
          vignette: false,
        }}
        render={{
          thumbnail: ({ slide, rect }) => {
            const isVideo = slide.type === "video";
            const src = isVideo ? slide.poster || slide.sources?.[0]?.src : slide.src;

            return (
              <div
                style={{
                  width: rect.width,
                  height: rect.height,
                  overflow: "hidden",
                  borderRadius: "4px",
                  backgroundColor: "#f3f4f6",
                }}
              >
                {isVideo ? (
                  <video
                    src={src}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    muted
                    preload="metadata"
                    crossOrigin="anonymous"
                  />
                ) : (
                  <img
                    src={src}
                    alt="Thumbnail"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    loading="lazy"
                  />
                )}
              </div>
            );
          },
        }}
        zoom={{
          maxZoomPixelRatio: 3,
          zoomInMultiplier: 2,
          doubleClickMaxStops: 3,
          keyboardMoveDistance: 50,
          wheelZoomDistanceFactor: 100,
          pinchZoomDistanceFactor: 100,
          scrollToZoom: true,
        }}
        styles={{
          container: {
            background: "rgba(0, 0, 0, 0.8)",
          },
          thumbnailsContainer: {
            background: "rgba(0, 0, 0, 0.8)",
          },
          thumbnail: {
            border: "2px solid transparent",
          },
          thumbnailsTrack: {
            padding: "16px",
          },
        }}
      />

      <style>
        {`
          .yarl__video_container video {
            max-width: max-content !important;
          }
          .yarl__thumbnails_thumbnail_active {
            border-color: #3b82f6 !important;
          }
          .yarl__thumbnails_thumbnail {
            background-color: #f3f4f6 !important;
            overflow: hidden !important;
          }
          .yarl__thumbnails_thumbnail img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
          }
          .yarl__thumbnails_thumbnail video {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
          }
          .yarl__navigation_prev,
          .yarl__navigation_next {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: rgba(0, 0, 0, 0.5) !important;
            color: white !important;
            border: none !important;
            width: 44px !important;
            height: 44px !important;
            border-radius: 50% !important;
            margin: 16px !important;
            cursor: pointer !important;
            transition: background-color 0.2s ease !important;
          }
          .yarl__navigation_prev:hover,
          .yarl__navigation_next:hover {
            background: rgba(0, 0, 0, 0.7) !important;
          }
          .yarl__navigation_prev:disabled,
          .yarl__navigation_next:disabled {
            opacity: 0.3 !important;
            cursor: not-allowed !important;
          }
        `}
      </style>
    </>
  );
};

export default ViewService;
