// pages/ExamplePage.tsx
import { themes } from "../../../../theme";
import ScrollButton from "@/components/bottomArrow";
import { useRef } from "react";
import ScrollButtonRight from "@/components/rightArrow";
import CategoryComp from "@/globalComponents/homeComp/categoryComp";
import useAuth from "@/hook";
import ProfileCard from "./profileCard";
import ScrollButtonLeft from "@/components/leftArrow";
import { useFilter } from "@/context/FilterContext";

const data = [
  {
    img: "/assets/img.svg",
    title: "My Feed",
    backgroundColor: "#000000",
  },
  {
    img: "/assets/music.svg",
    title: "Music",
    backgroundColor: "#E5B045",
  },
  {
    img: "/assets/litrature.svg",
    title: "Literature",
    backgroundColor: "#CF5943",
  },
  {
    img: "/assets/art.svg",
    backgroundColor: "#3C5F9A",
    title: "Art",
  },
  {
    img: "/assets/film.svg",
    title: "Film & Photography",
    backgroundColor: "#46B933",
  },
  {
    img: "/assets/Theatre.svg",
    title: "Theatre & Performance",
    backgroundColor: "#E073D2",
  },
  {
    img: "/assets/multi.svg",
    title: "Multidisciplinary",
    backgroundColor: "#5331BC",
  },
  {
    img: "/assets/groups.svg",
    title: "Groups",
    backgroundColor: "#616770",
  },
];

const ProfileHome = () => {
  const user = useAuth(); // Assuming useAuth returns null or undefined if the user is not logged in
  const { filters } = useFilter();
  const scrollRef = useRef<HTMLDivElement>(null);

  return (
    <div className="relative p-0">
      <ScrollButton scrollRef={scrollRef} />
      <ScrollButtonRight scrollRef={scrollRef} />
      <ScrollButtonLeft scrollRef={scrollRef} />

      <div
        ref={scrollRef}
        className="overflow-y-auto p-0 h-[calc(100vh-205px)] hide-scroll"
        // style={{ maxHeight: "100vh" }}
      >
        <div className="space-y-4">
          <div
            className={`flex flex-row max-md:flex-col w-full gap-3 hide-scroll bg-white max-md:h-full ${
              user.isLogin ? "pl-2" : ""
            }`}
            ref={scrollRef}
          >
            {(() => {
              const filteredData = data.filter((item) => {
                // Apply category filtering
                if (filters.categories && filters.categories.length > 0) {
                  return filters.categories.includes(item.title);
                }
                return true; // Show all categories if no filter is applied
              });

              // Show message when no categories match the filter
              if (
                filters.categories &&
                filters.categories.length > 0 &&
                filteredData.length === 0
              ) {
                return (
                  <div className="w-full flex items-center justify-center min-h-[400px]">
                    <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200 max-w-md">
                      <div className="mb-4">
                        <svg
                          className="w-16 h-16 text-gray-400 mx-auto"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                          />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No matching categories
                      </h3>
                      <p className="text-gray-500">
                        No profiles found for the selected categories. Try adjusting your filter
                        settings.
                      </p>
                    </div>
                  </div>
                );
              }

              return filteredData.map((item: any, index: any) => {
                return (
                  <div className="" key={`${item.title}-${filters.categories?.join(",") || "all"}`}>
                    {!user.isLogin && !(item.title == "My Feed") && (
                      <>
                        <CategoryComp item={item} />
                      </>
                    )}

                    {user.isLogin && (
                      <>
                        <CategoryComp item={item} />
                      </>
                    )}

                    <div
                      className="overflow-auto hide-scroll h-full mt-0"
                      style={{ minHeight: "100%" }}
                      // Attach ref only to the second item
                    >
                      <div className="md:min-w-[350px] md:max-w-[350px] w-[350px]sa">
                        {Array.from({ length: 1 }).map((_, indexs) => (
                          <div key={indexs}>
                            {Object.entries(themes).map(
                              ([themeName, themeProperties]) =>
                                themeProperties.title === item.title && (
                                  <div key={themeName}>
                                    {!user.isLogin && !(item.title == "My Feed") && (
                                      <>
                                        <ProfileCard
                                          border={themeProperties.backgroundColor}
                                          themeProperties={themeProperties}
                                        />
                                      </>
                                    )}

                                    {user.isLogin && (
                                      <>
                                        <ProfileCard
                                          border={themeProperties.backgroundColor}
                                          themeProperties={themeProperties}
                                        />
                                      </>
                                    )}
                                  </div>
                                )
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              });
            })()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHome;
