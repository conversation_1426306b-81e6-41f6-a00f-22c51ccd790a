"use client";
import { getAllPostsTest } from "@/services/postService";
import { useCallback, useEffect, useRef, useState } from "react";
import { themes } from "../../../../theme";
import { Play } from "react-feather";
import LensData from "@/hook/lensData";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getUserById, getUserByPostId } from "@/services/usersServices";
import { PostsQuery } from "@/graphql/test/generated";
import { useRouter } from "next/navigation";
import Link from "next/link";
import PostSkeletonLoader from "./PostSkeletonLoader";
import LazyMedia from "@/components/LazyMedia";
import { useFilter } from "@/context/FilterContext";

const PostMobileView = (props: any) => {
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  const { profileData: profileDataById } = useProfile(user?.userId || "");

  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const isFetched = useRef<boolean>(false);
  const category = props.themeProperties.title.toLowerCase();

  const { publicationsData }: { publicationsData: PostsQuery | undefined } = LensData(category);

  // console.log({ publicationsData });

  // Generate public URL for Firebase Storage

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    // Check if postFile is a valid string
    if (!postFile) {
      return undefined; // Return undefined if postFile is null, undefined, or an empty string
    }

    // If the postFile already includes the baseUrl, return it as-is
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    // Otherwise, construct the URL and return it
    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const fetchAllPosts = useCallback(async () => {
    try {
      setLoading(true);

      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();
      const response = await getAllPostsTest(serviceFilters);

      if (response?.posts) {
        const categorizedData: Record<string, any[]> = {};
        const myFeed: any[] = [];
        const myFeedSet = new Set();

        // To remove duplicate posts globally
        const uniquePostsMap = new Map<string, any>();

        // Fetch user followers only once if logged in
        let userFollowings: string[] = [];
        if (user?.isLogin) {
          const userResponse = await getUserById(user?.userId);
          if (userResponse.success) {
            userFollowings = userResponse.user?.following || [];
          }
        }

        // Process posts asynchronously
        await Promise.all(
          response.posts.map(async (post: any) => {
            if (uniquePostsMap.has(post.id)) return; // Skip duplicate posts

            const [postCategory, userId] = await Promise.all([post?.category, post?.user_id]);

            const normalizedCategory =
              post.category === "Storytelling" ? "Literature" : post.category;

            // Store unique posts
            uniquePostsMap.set(post.id, {
              ...post,
              category: normalizedCategory,
            });

            if (!categorizedData[normalizedCategory]) {
              categorizedData[normalizedCategory] = [];
            }
            categorizedData[normalizedCategory].push(post);
            // Add to "My Feed" if the user follows the post's author
            if (
              user?.isLogin &&
              userId &&
              userFollowings.includes(userId) &&
              !myFeedSet.has(post.id)
            ) {
              myFeedSet.add(post.id);
              myFeed.push({ ...post, category: normalizedCategory });
            }
          })
        );

        // Sort posts in each category by `created` field
        for (const category in categorizedData) {
          categorizedData[category].sort(
            (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime()
          );
        }

        // Add "My Feed" if user is logged in
        if (user?.isLogin) {
          categorizedData["My Feed"] = myFeed;
        }

        // console.log({ categorizedData });

        setCategoryData(categorizedData);

        // setPostsLoaded(true);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [profileDataById, filters]); // Keep dependency consistent with events function

  useEffect(() => {
    fetchAllPosts();
  }, [fetchAllPosts]); // Ensures function runs only when dependencies change

  const router = useRouter();

  const handleClick = async (category: string, idparam: string) => {
    try {
      const data = await getUserByPostId(idparam);
      const userId = data?.users?.[0]?.id || "unknown";
      router.push(`/browse/${category}/${idparam}%20${userId}`);
    } catch (error) {
      console.error("Error fetching user:", error);
    }
  };

  return (
    // <>
    //   {loading ? (
    //     <PostSkeletonLoader
    //       isScroll={props.isScroll}
    //       borderColor={props.borderColor}
    //       count={5} // Always show 5 items per row
    //     />
    //   ) : (
    //     <div className="w-full">
    //       {Object.entries(themes).map(([themeName, themeProperties]) => (
    //         <div key={themeName} className="">
    //           {props.themeProperties.title === themeProperties.title && (
    //             <div>
    //               {categoryData[themeProperties.title]?.length > 0 ? (
    //                 <div>
    //                   {props.isScroll ? (
    //                     <div>
    //                       <div className="mt-0 flex flex-row gird grid-rows-2 gap-x-[2px]">
    //                         {categoryData[themeProperties.title]
    //                           .slice(5, categoryData[themeProperties.title].length) // Display only the first 5 items
    //                           .map((post, index) => (
    //                             <div key={index} className="cursor-pointer">
    //                               <Link
    //                                 className=""
    //                                 href={`/browse/${
    //                                   post?.category === "Storytelling"
    //                                     ? "Literature"
    //                                     : post?.category
    //                                 }/${post?.id}%20${post?.user_id}`}
    //                                 prefetch={true}
    //                               >
    //                                 {post?.mediaType === "image" ? (
    //                                   <LazyMedia
    //                                     src={generateFileUrl(post?.postFile) || "/assets/noimg.png"}
    //                                     alt={`Post ${props.chunkIndex * 5 + 1}`}
    //                                     type="image"
    //                                     className=" min-h-[85px] min-w-[85px]  max-h-[85px] max-w-[85px] object-cover border-2 aspect-square"
    //                                     style={{
    //                                       borderColor: props.borderColor,
    //                                     }}
    //                                     placeholderClassName="bg-gray-100"
    //                                   />
    //                                 ) : post?.mediaType === "video" ? (
    //                                   <Link
    //                                     className=" relative "
    //                                     href={`/browse/${
    //                                       post?.category === "Storytelling"
    //                                         ? "Literature"
    //                                         : post?.category
    //                                     }/${post?.id}%20${post?.user_id}`}
    //                                     prefetch={true}
    //                                   >
    //                                     <LazyMedia
    //                                       src={generateFileUrl(post?.postFile)}
    //                                       type="video"
    //                                       className="min-h-[85px] min-w-[85px] max-h-[85px] max-w-[85px] object-cover border-2"
    //                                       style={{
    //                                         borderColor: props.borderColor,
    //                                       }}
    //                                       placeholderClassName="bg-gray-100"
    //                                       showPlayIcon={true}
    //                                       playIconClassName="top-0 right-0"
    //                                       controls={false}
    //                                       autoPlay={false}
    //                                       muted={true}
    //                                     />
    //                                   </Link>
    //                                 ) : null}
    //                               </Link>
    //                             </div>
    //                           ))}
    //                       </div>
    //                     </div>
    //                   ) : (
    //                     <div className="mt-0 flex flex-row flex-grow overflow-hidden">
    //                       {categoryData[themeProperties.title]
    //                         .slice(0, 5) // Display only the first 5 items
    //                         .map((post, index) => (
    //                           <div key={index} className="cursor-pointer row">
    //                             <div className="">
    //                               <Link
    //                                 href={`/browse/${
    //                                   post?.category === "Storytelling"
    //                                     ? "Literature"
    //                                     : post?.category
    //                                 }/${post?.id}%20${post?.user_id}`}
    //                                 prefetch={true}
    //                               >
    //                                 {post?.mediaType === "image" ? (
    //                                   <LazyMedia
    //                                     src={generateFileUrl(post?.postFile) || "/assets/noimg.png"}
    //                                     alt={`Post ${props.chunkIndex * 5 + 1}`}
    //                                     type="image"
    //                                     className=" min-h-[85px] min-w-[85px]  max-h-[85px] max-w-[85px] object-cover border-2 aspect-square"
    //                                     style={{
    //                                       borderColor: props.borderColor,
    //                                     }}
    //                                     placeholderClassName="bg-gray-100"
    //                                   />
    //                                 ) : post?.mediaType === "video" ? (
    //                                   <div
    //                                     className=" relative "
    //                                     onClick={() =>
    //                                       handleClick(
    //                                         post?.category === "Storytelling"
    //                                           ? "Literature"
    //                                           : post?.category,
    //                                         post?.id
    //                                       )
    //                                     }
    //                                   >
    //                                     <LazyMedia
    //                                       src={generateFileUrl(post?.postFile)}
    //                                       type="video"
    //                                       className="min-h-[85px] min-w-[85px] max-h-[85px] max-w-[85px] object-cover border-2"
    //                                       style={{
    //                                         borderColor: props.borderColor,
    //                                       }}
    //                                       placeholderClassName="bg-gray-100"
    //                                       showPlayIcon={true}
    //                                       playIconClassName="top-0 right-0"
    //                                       controls={false}
    //                                       autoPlay={false}
    //                                       muted={true}
    //                                     />
    //                                   </div>
    //                                 ) : null}
    //                               </Link>
    //                             </div>
    //                             <p>{post?.title}</p> {/* Display post title */}
    //                           </div>
    //                         ))}
    //                     </div>
    //                   )}
    //                 </div>
    //               ) : (
    //                 <div className="flex flex-col items-center justify-center p-4">
    //                   <div className="w-16 h-16 mb-2 rounded-full bg-gray-100 flex items-center justify-center">
    //                     <svg
    //                       className="w-8 h-8 text-gray-400"
    //                       fill="none"
    //                       stroke="currentColor"
    //                       viewBox="0 0 24 24"
    //                       xmlns="http://www.w3.org/2000/svg"
    //                     >
    //                       <path
    //                         strokeLinecap="round"
    //                         strokeLinejoin="round"
    //                         strokeWidth="2"
    //                         d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
    //                       ></path>
    //                     </svg>
    //                   </div>
    //                   <p className="text-gray-500 text-center">
    //                     No posts available in this category.
    //                   </p>
    //                 </div>
    //               )}
    //             </div>
    //           )}
    //         </div>
    //       ))}
    //     </div>
    //   )}
    // </>
    <p>PostMobileView</p>
  );
};

export default PostMobileView;
