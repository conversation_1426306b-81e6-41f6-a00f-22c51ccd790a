import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import {
  updateTransaction,
  getTransactionByStripeSessionId,
  getEscrowTransactionByOrderId,
} from "@/services/transactionService";
import { processPaymentSuccess } from "@/services/postPaymentService";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET as string;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get("stripe-signature") as string;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error("Webhook signature verification failed:", err);
      return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
    }

    console.log("Received Stripe webhook event:", event.type);

    // Handle the event
    switch (event.type) {
      case "payment_intent.succeeded":
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case "payment_intent.requires_action":
        await handlePaymentIntentRequiresCapture(
          (event as any).data.object as Stripe.PaymentIntent
        );
        break;

      case "payment_intent.payment_failed":
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case "invoice.payment_succeeded":
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case "customer.created":
        await handleCustomerCreated(event.data.object as Stripe.Customer);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return NextResponse.json({ error: "Webhook handler failed" }, { status: 500 });
  }
}

// Removed handleCheckoutSessionCompleted - no longer using checkout sessions

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log("Processing payment intent succeeded:", paymentIntent.id);

    // Extract metadata
    const orderId = paymentIntent.metadata?.orderId;
    const userId = paymentIntent.metadata?.userId;
    const sellerId = paymentIntent.metadata?.sellerId;
    const isEscrow = paymentIntent.metadata?.isEscrow === "true";

    if (!orderId || !userId || !sellerId) {
      console.log("Missing required metadata for post-payment processing");
      return;
    }

    // Get charge ID
    const charges = paymentIntent.charges?.data;
    const chargeId = charges && charges.length > 0 ? charges[0].id : undefined;

    // Use our comprehensive post-payment processing
    const result = await processPaymentSuccess({
      paymentIntentId: paymentIntent.id,
      orderId,
      transactionId: paymentIntent.metadata?.transactionId,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      isEscrow,
      userId,
      sellerId,
      userEmail: paymentIntent.metadata?.userEmail,
      userName: paymentIntent.metadata?.userName,
      sellerName: paymentIntent.metadata?.sellerName,
      chargeId
    });

    if (result.success) {
      console.log("✅ Post-payment processing completed successfully via webhook");
    } else {
      console.error("❌ Post-payment processing failed via webhook:", result.error);
    }

  } catch (error) {
    console.error("Error handling payment intent succeeded:", error);
  }
}

async function handlePaymentIntentRequiresCapture(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log("Processing payment intent requires capture:", paymentIntent.id);

    // Check if this is an escrow payment
    if (paymentIntent.metadata?.isEscrow === "true") {
      console.log("Escrow payment authorized - waiting for capture trigger");

      const orderId = paymentIntent.metadata.orderId;
      if (orderId) {
        // Update transaction to reflect authorization status
        const transactionResult = await getEscrowTransactionByOrderId(orderId);
        if (transactionResult.success && transactionResult.transaction) {
          await updateTransaction(transactionResult.transaction.id, {
            currentStage: "pending", // Payment authorized but not captured
            metadata: {
              ...transactionResult.transaction.metadata,
              paymentAuthorizedAt: new Date().toISOString(),
              paymentIntentStatus: paymentIntent.status,
              awaitingCapture: true,
            },
          });

          console.log(
            `Escrow transaction ${transactionResult.transaction.id} updated - payment authorized, awaiting capture`
          );
        }
      }
    }
  } catch (error) {
    console.error("Error handling payment intent requires capture:", error);
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log("Processing payment intent failed:", paymentIntent.id);

    // You can add logic here to handle failed payments
    // For example, notifying the user, updating transaction status, etc.
  } catch (error) {
    console.error("Error handling payment intent failed:", error);
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log("Processing invoice payment succeeded:", invoice.id);

    // Handle invoice payment success
    // This is useful for subscription payments
  } catch (error) {
    console.error("Error handling invoice payment succeeded:", error);
  }
}

async function handleCustomerCreated(customer: Stripe.Customer) {
  try {
    console.log("Processing customer created:", customer.id);

    // You can add logic here to sync customer data
    // For example, updating user records in Firebase
  } catch (error) {
    console.error("Error handling customer created:", error);
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json({ message: "Stripe webhook endpoint" });
}

export async function PUT() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
