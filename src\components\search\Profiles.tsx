import React, { useEffect, useState, useRef, useCallback } from "react";
import { FilterSearchManager, ProfileSearchBy } from "@/services/filtersServices";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import EmptyState from "@/components/EmptyState";
import { themes } from "../../../theme";

const Profiles = ({
  searchFor,
  searchBy,
  search,
  SEARCH_FOR,
  SEARCH_BY_MAP,
  onClose,
  filters,
}: any) => {
  const [profiles, setProfiles] = useState<any[]>([]); // loaded profiles
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextStartAfterAddedAt, setNextStartAfterAddedAt] = useState<any>(undefined);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const PAGE_SIZE = 50;

  // Fetch profiles (initial or paginated)
  const fetchProfiles = useCallback(
    async (isInitial = false) => {
      let filterBy;
      if (searchBy === "Profile name") {
        filterBy = ProfileSearchBy.PROFILE_NAME;
      } else if (searchBy === "Hashtag") {
        filterBy = ProfileSearchBy.HASHTAGS;
      } else if (searchBy === "Location") {
        filterBy = ProfileSearchBy.LOCATION;
      } else {
        setProfiles([]);
        setHasMore(false);
        setNextStartAfterAddedAt(undefined);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const resp = await FilterSearchManager.getInstance().GetProfileByFilters({
          payload: {
            filterBy,
            searchTerm: search,
            limit: PAGE_SIZE,
            startAfterAddedAt: isInitial ? undefined : nextStartAfterAddedAt,
            filters,
          },
        });
        if (isInitial) {
          setProfiles(resp.profiles || []);
        } else {
          setProfiles((prev) => [...prev, ...(resp.profiles || [])]);
        }
        setNextStartAfterAddedAt(resp.nextStartAfterAddedAt);
        setHasMore(resp.hasMore);
      } catch (error: any) {
        setError(error && error.message ? error.message : "Error fetching profiles");
      } finally {
        setLoading(false);
      }
    },
    [search, searchBy, nextStartAfterAddedAt, filters]
  );

  // Initial fetch or when search/searchBy changes
  useEffect(() => {
    setProfiles([]);
    setNextStartAfterAddedAt(undefined);
    setHasMore(true);
    fetchProfiles(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, searchBy, filters]);

  // Scroll handler for backend pagination
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || loading || !hasMore) return;
    if (container.scrollHeight - container.scrollTop - container.clientHeight < 100) {
      fetchProfiles(false);
    }
  }, [loading, hasMore, fetchProfiles]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const hasProfiles = Array.isArray(profiles) && profiles.length > 0;

  return (
    <div ref={containerRef} className="overflow-y-auto  h-[calc(100vh-200px)] min-h-0">
      {/* <div>Profiles search content goes here.</div>
      <div>searchFor: {searchFor}</div>
      <div>searchBy: {searchBy}</div>
      <div>search: {search}</div>
      <div>SEARCH_FOR: {JSON.stringify(SEARCH_FOR)}</div>
      <div>SEARCH_BY_MAP: {JSON.stringify(SEARCH_BY_MAP)}</div> */}
      {hasProfiles ? (
        <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
          {profiles
            .filter(
              (item: any) => Array.isArray(item.categories) && item.categories.length > 0
              // ?item.categories[0] !== 'Customer': null
            )
            .map((post: any) => {
              // Only render if post.category matches a theme title
              const matchedTheme = Object.entries(themes).find(
                ([_key, theme]: [string, any]) =>
                  (Array.isArray(post.categories) && post.categories.length > 0
                    ? post.categories[0] === "Storytelling"
                      ? "Literature"
                      : post.categories[0]
                    : null) === theme.title
              );
              if (!matchedTheme) return null;
              return (
                <div onClick={onClose} className=" border-b-2">
                  <GlobalProfileCard
                    key={post.id}
                    location={post.location}
                    profile_name={post.profile_name}
                    avatar={post.avatar}
                    id={post.id}
                    themeProperties={matchedTheme[1]}
                  />
                </div>
              );
            })}
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-full py-8">
          <span className="loader">Loading...</span>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <EmptyState
            type="profiles"
            title="No profiles found"
            message="Try adjusting your search or filter to find profiles."
          />
        </div>
      )}
      {/* Show loading spinner at bottom if loading more */}
      {hasProfiles && loading && (
        <div className="flex justify-center items-center py-4">
          <span className="loader">Loading...</span>
        </div>
      )}
      {/* Show 'No more data found' if all data loaded */}
      {hasProfiles && !hasMore && !loading && (
        <div className="flex justify-center items-center py-4 text-gray-500">
          No more data found
        </div>
      )}
      {/* Show error if any */}
      {error && <div className="text-red-500 text-center py-2">{error}</div>}
    </div>
  );
};

export default Profiles;
