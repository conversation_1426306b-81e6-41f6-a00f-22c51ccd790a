"use client";
// import localFont from "next/font/local";
import "./globals.css";
import "../styles/payment.css";
import { ThemeProvider } from "../../themeContext";
import NavBar from "@/components/navbar";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { metadata } from "./layoout.server";
import { Providers } from "@/lib/Providers";
import GlobalSignInButton from "@/components/GlobalSignInButton";
import { createConfig, WagmiProvider } from "wagmi";
import getRpc, { CHAIN, NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID } from "@/lib/constant";
import { injected, walletConnect } from "wagmi/connectors";
import React from "react";
import FirebaseProvider from "@/context/FirebaseContext";
import LoadingProvider from "@/context/LoadingContext";
import GoogleMapsProvider from "@/context/GoogleMapsContext";
import FilterProvider from "@/context/FilterContext";
import AuthWatcher from "@/components/AuthProvider";


const connectors = [
  injected(),
  walletConnect({
    projectId: NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
  }),
];

const config = createConfig({
  chains: [CHAIN],
  transports: {
    [CHAIN.id]: getRpc({ mainnet: true }),
  },
  connectors,
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const queryClient = new QueryClient();

  return (
    <html lang="en">
      <head>
        <title>{metadata?.title || "Default Title"}</title>
        <meta name="description" content={metadata.description} />
      </head>

      <body>
        <Providers>
          <LoadingProvider>
            <FirebaseProvider>
              <GoogleMapsProvider>
                <FilterProvider>
                  <WagmiProvider config={config}>
                    <QueryClientProvider client={queryClient}>
                      <ThemeProvider>
                        <NavBar />
                        <AuthWatcher>{children}</AuthWatcher>
                        <GlobalSignInButton />
                      </ThemeProvider>
                    </QueryClientProvider>
                  </WagmiProvider>
                </FilterProvider>
              </GoogleMapsProvider>
            </FirebaseProvider>
          </LoadingProvider>
        </Providers>
      </body>
    </html>
  );
}
