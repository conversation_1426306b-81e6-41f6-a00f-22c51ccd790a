// services/chatService.ts

import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  Timestamp,
  query,
  where,
  serverTimestamp,
  setDoc,
  orderBy,
  onSnapshot,
  limit,
  or,
  getDoc,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";

// Interface
interface SendMailProps {
  from?: string; // e.g. "AMUZN <<EMAIL>>"
  toMail: string; // recipient email
  subject?: string; // email subject
  html?: string; // HTML body
  text?: string; // optional plain text body
  message: Record<string, any>; // extra metadata (optional)
  type:EmailType
}
type EmailType =
  | "profile_deleted"
  | "profile_reported"
  | "order_update"
  | "invoice_request"
  | "stripe_connect"
  | "post_reported";

interface EmailPayload {
  type: EmailType;
  to: string; // recipient
  data?: Record<string, any>;
}
///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// /////

export class MailServiceManager {
  private MAIL_COLLECTION = "mail";

  static instance: MailServiceManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new MailServiceManager();
    }
    return this.instance;
  }

  async sendMail({ from, toMail, subject, html, text, message = {} , type }: SendMailProps) {
    try {
      const { db } = await initFirebase();
      const mailRef = collection(db, "mail");

      const {from , to, subject , html} = this.buildEmail({
        to:toMail , type , data:message
      })

      await addDoc(mailRef, {
        to,
        from,
        message: {
          subject,
          html,
          text: text ?? html.replace(/<[^>]+>/g, ""), // fallback plain text
          ...message,
        },
        createdAt: serverTimestamp(),
      });

      console.log(`📧 Email queued to ${to}`);
    } catch (error) {
      console.error("sendMail failed:", error);
      throw new Error("sendMail failed");
    }
  }

  buildEmail({ type, to, data = {} }: EmailPayload) {
    const from = "<EMAIL>";

    let subject = "";
    let html = "";

    switch (type) {
      case "profile_deleted":
        subject = "Profile deleted";
        html = `
        <div>
          <p>Profile deleted: ${data.profileId}</p>
          <p>Reason: ${data.reason}</p>
        </div>`;
        break;

      case "profile_reported":
        subject = "Profile reported";
        html = `
        <div>
          <p>Profile reported: ${data.reportedName} (user UID: ${data.reportedUid})</p>
          <p>Profile reported by: ${data.reporterName} (user UID: ${data.reporterUid})</p>
          <p>Report reason: ${data.reason}</p>
          <p>Comment: ${data.comment}</p>
        </div>`;
        break;
      case "post_reported":
        subject = "Post reported";
        html = `
        <div>
          <p>Post owner: ${data.reportedName} (user UID: ${data.reportedUid})</p>
          <p>Post reported by: ${data.reporterName} (user UID: ${data.reporterUid})</p>
          <p>Post : ${data?.post_id}</p>
          <p>Report reason: ${data.reason}</p>
          <p>Comment: ${data.comment}</p>
        </div>`;
        break;

      case "order_update":
        subject = `AMUZN Order status update - ${data.orderId}: ${data.status}`;
        html = `<pre style="font-family: inherit; white-space: pre-wrap;">${data.message}</pre>`;
        break;

      case "invoice_request":
        subject = `Invoice - customer tax information request - ${data.orderId}`;
        html = `
        <div>
          <p>Hello,</p>
          <p>Thank you for your request for an invoice.</p>
          <p>Please provide your tax details below as required in order to issue you an invoice.</p>
          <ul>
            <li>Legal name</li>
            <li>Street address</li>
            <li>Town/City</li>
            <li>Postcode (e.g. EC1A1BB)</li>
            <li>Country</li>
            <li>VAT number (e.g. *********)</li>
          </ul>
          <p>An invoice will be created and emailed to you including your details provided above.</p>
          <p>If any questions please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
          <p>Your AMUZN Team</p>
        </div>`;
        break;

      // ask louis from where to trigger this 
      case "stripe_connect":
        subject = "Connect Stripe to sell on AMUZN";
        html = `
        <div>
          <p>Don't forget to connect Stripe to your Amuzn profile so you can start selling services.</p>
          <p>Go to <b>Connect Account</b> in Amuzn app left menu and follow the instructions.</p>
        </div>`;
        break;
    }

    return { from, to, subject, html };
  }
}
