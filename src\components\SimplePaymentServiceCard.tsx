import * as React from "react";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDuration, getServiceById } from "@/services/serviceService";
import { getCurrencySymbol } from "@/services/currencyService";
import { getOrderById } from "@/services/ordersServices";
interface GlobalCardProps {
  orderId: string; //
  border?: string;
}

export function SimplePaymentServiceCard({ orderId, border }: GlobalCardProps) {
  const [order, setOrder] = React.useState<any | null>(null);
  const [orderCustomizations, setOrderCustomizations] = React.useState<any | null>(null);
  const [totalAmount, setTotalAmount] = React.useState<number>(0);

  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  // console.log({ orderId });

  // Function to calculate total amount including service and customizations
  const calculateTotalAmount = (serviceData: any, selectedCustomizations: string[] | undefined) => {
    if (!serviceData) return;

    // Base service price with tax
    const servicePrice = parseFloat(serviceData.price || "0") / (1 - 0.16);
    let customizationsTotal = 0;

    // Calculate customizations total
    if (
      selectedCustomizations &&
      selectedCustomizations.length > 0 &&
      serviceData.customizations_array
    ) {
      customizationsTotal = selectedCustomizations.reduce((total, customizationId) => {
        const customization = serviceData.customizations_array.find(
          (c: any) => c.id === customizationId
        );
        if (customization) {
          const customizationPrice = parseFloat(customization.price || "0") / (1 - 0.16);
          return total + customizationPrice;
        }
        return total;
      }, 0);
    }

    // Subtotal
    const subtotal = servicePrice + customizationsTotal;

    // Add 4% and fix floating-point precision
    const total = Number((subtotal * 1.04).toFixed(2));

    setTotalAmount(total);
  };

  React.useEffect(() => {
    async function fetchOrder() {
      try {
        setLoading(true);
        const res = await getOrderById(orderId);
        setOrderCustomizations(res?.order?.selectedCustomizations);

        const resps = await getServiceById(res?.order?.serviceId || "");

        setOrder(resps.service);

        // Calculate total amount
        calculateTotalAmount(resps.service, res?.order?.selectedCustomizations);
      } catch (err: any) {
        console.error("Error fetching order/service:", err);
        setError(err.message || "Failed to load order");
      } finally {
        setLoading(false);
      }
    }

    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  if (loading) {
    return <p>Loading order...</p>;
  }

  if (error) {
    return <p className="text-red-500">Error: {error}</p>;
  }

  if (!order) {
    return <p>No order found</p>;
  }

  // console.log(order);

  return (
    <>
      <div className="w-full mt-0 p-0 pb-2">
        <div>
          <p className="text-base font-bold text-primary my-2">
            {order?.title || "Untitled Service"}
          </p>
        </div>
        <div className="flex justify-between">
          <p className="text-subtitle">Approximate time</p>
          <p className="text-lg font-bold text-subtitle">
            {formatDuration(order?.duration || "0")}
          </p>
        </div>
        <div className="flex justify-between">
          <p className="text-subtitle">Service subtotal</p>
          <p className="text-lg text-subtitle">
            {getCurrencySymbol(order?.currency ?? "gbp")}
            {(parseFloat(order?.price || "0") / (1 - 0.16)).toFixed(2)}
          </p>
        </div>
      </div>

      {/* Customizations */}
      {orderCustomizations && orderCustomizations.length > 0 && (
        <p className="text-lg font-bold text-gray-700">Customizations</p>
      )}
      {orderCustomizations &&
        orderCustomizations.length > 0 &&
        order?.customizations_array &&
        orderCustomizations.map((customizationId: string, index: number) => {
          const customization = order?.customizations_array?.find(
            (c: any) => c.id === customizationId
          );
          if (!customization) {
            console.log(`Customization not found for ID: ${customizationId}`);
            return null;
          }

          return (
            <div key={customizationId} className="flex justify-between">
              <div
                className={
                  (orderCustomizations ?? []).length - 1 === index
                    ? "flex gap-3 w-full border-b-2 border-[#7C7C7C]"
                    : "flex gap-3 w-full "
                }
              >
                <p className="text-sm font-bold text-primary my-2 line-clamp-1 w-10">
                  #{index + 1}
                </p>

                <div className="w-full mt-0 p-0  pb-4">
                  <div>
                    <p className="text-sm font-bold text-primary my-2 line-clamp-1">
                      {customization.title}
                    </p>

                    {/* </Link> */}
                  </div>
                  <div className="flex justify-between ">
                    <p className="text-subtitle">Approximate time</p>
                    <p className="text-lg font-bold text-subtitle">
                      {formatDuration(customization.duration || "0")}
                    </p>
                  </div>
                  <div className="flex justify-between ">
                    <p className="text-subtitle">Customization subtotal</p>
                    <p className="text-lg text-subtitle">
                      {getCurrencySymbol(order.currency ?? "gbp")}
                      {(parseFloat(customization.price || "0") / (1 - 0.16)).toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

      {/* Total Amount Summary */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg border-2 border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <span className="text-lg font-semibold text-gray-700">Total Amount</span>

            <div className="mt-0 text-xs text-gray-600">
              <p className="text-sm">Includes(4% transaction fee)</p>
            </div>
          </div>
          <span className="text-2xl font-bold text-primary">
            {getCurrencySymbol(order?.currency ?? "gbp")}
            {totalAmount.toFixed(2)}
          </span>
        </div>
        <div className="mt-2 text-sm text-gray-600">
          <p>Includes service and {orderCustomizations?.length || 0} customization(s)</p>
        </div>
      </div>
    </>
  );
}
