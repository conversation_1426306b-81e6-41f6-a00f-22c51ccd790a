"use client";
import { useCallback, useEffect, useRef, useState } from "react";
import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
import { getAllEvents } from "@/services/eventsServices";
import { getUserByEventId, getUserById } from "@/services/usersServices";
import { themes } from "../../../../theme";
import { useRouter } from "next/navigation";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import EventCardSkeleton from "@/components/CardSkeleton/EventCardSkeleton";
import { useFilter } from "@/context/FilterContext";

const EventsCard = (props: any) => {
  const router = useRouter();
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  const { profileData } = useProfile(user?.userId || "");
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [localLoading, setLocalLoading] = useState<boolean>(true);

  const getUserID = async (eventId: string) => {
    try {
      const response = await getUserByEventId(eventId);
      if (response.success) {
        const userData: any = response.users;
        // console.log(userData[0]?.id);
        // setUserID(userData[0]?.id);
        router.push(
          `/profile/amuzn/${userData[0]?.profile_name?.replace(/\s+/g, "-")}?view=Events`
        );
        return userData[0]?.id; // Return the category
      }
    } catch (error) {
      // console.error("Error fetching user category:", error);
    }
    return null; // Return null if the category couldn't be determined
  };

  const fetchAllPosts = useCallback(async () => {
    try {
      setLoading(true);
      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();
      const response = await getAllEvents(serviceFilters);

      if (response?.events) {
        const categorizedData: Record<string, any[]> = {};
        const myFeed: any[] = []; // Array to store selected posts with category info
        const myFeedSet = new Set();

        // Fetch user ID and category for each event and organize data
        await Promise.all(
          response.events.map(async (post: any) => {
            const [postCategory, userId] = await Promise.all([
              getUserCategory(post?.id),
              getUserIDForCheck(post?.id),
            ]);

            if (postCategory) {
              const normalizedCategory =
                postCategory === "Storytelling" ? "Literature" : postCategory;

              if (!categorizedData[normalizedCategory]) {
                categorizedData[normalizedCategory] = [];
              }

              categorizedData[normalizedCategory].push(post);

              // Check if the user is in profileData.followers before adding to My Feed
              if (user?.isLogin) {
                const response = await getUserById(user?.userId);
                const resp = response.user;
                if (response.success) {
                  // console.log(resp?.followers);
                }
                if (userId && resp?.following.includes(userId) && !myFeedSet.has(post.id)) {
                  myFeedSet.add(post.id);
                  myFeed.push({ ...post, category: normalizedCategory });
                }
              }
            }
          })
        );

        // Sort posts in each category by the `created` field
        for (const category in categorizedData) {
          categorizedData[category].sort(
            (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime()
          );
        }

        // Add My Feed to categorizedData
        if (user?.isLogin) {
          categorizedData["My Feed"] = myFeed;
        }

        setCategoryData(categorizedData);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [profileData, filters]); // Dependency array to prevent unnecessary re-renders

  useEffect(() => {
    fetchAllPosts();
  }, [fetchAllPosts]); // Ensures function runs only when dependencies change

  // Force initial loading state and ensure it persists
  useEffect(() => {
    // Always start with loading state true
    setLocalLoading(true);

    let timer: NodeJS.Timeout;

    // Only transition to non-loading state when data is ready and loading is false
    if (!loading && categoryData && Object.keys(categoryData).length > 0) {
      timer = setTimeout(() => {
        setLocalLoading(false);
      }, 1000); // Longer delay to ensure skeleton is visible
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [loading, categoryData]);

  // Function to get user category by event ID
  const getUserCategory = async (eventId: string) => {
    try {
      const response = await getUserByEventId(eventId);
      if (response.success) {
        const userData: any = response.users;
        return userData[0]?.categories[0]; // Return the category
      }
    } catch (error) {
      console.error("Error fetching user category:", error);
    }
    return null;
  };

  // Function to get user ID for checking against followers
  const getUserIDForCheck = async (eventId: string) => {
    try {
      const response = await getUserByEventId(eventId);
      if (response.success) {
        const userData: any = response.users;
        return userData[0]?.id; // Return the user ID
      }
    } catch (error) {
      console.error("Error fetching user ID:", error);
    }
    return null;
  };

  return (
    <>
      {localLoading ? (
        <div className="">
          {/* Show 3 skeleton cards stacked vertically */}
          {[1, 2, 3].map((num) => (
            <div key={num} className="mb-4">
              <EventCardSkeleton count={1} showGrid={false} />
            </div>
          ))}
        </div>
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([themeName, themeProperties]) => (
            <div key={themeName}>
              {props.themeProperties.title === themeProperties.title && (
                <div>
                  {(() => {
                    const currentCategory = themeProperties.title;

                    // Check if filters are applied and if current category is in selected categories
                    if (filters.categories && filters.categories.length > 0) {
                      if (!filters.categories.includes(currentCategory)) {
                        // Current category is not in selected filters, show no data
                        return (
                          <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                            <p className="text-gray-500">No events found in this category.</p>
                          </div>
                        );
                      }
                    }

                    return categoryData[themeProperties.title]?.length > 0 ? (
                      categoryData[themeProperties.title].map((post, index) => (
                        <div key={index} className="mt-0">
                          {themeProperties.title == "My Feed" ? (
                            <div>
                              {Object.entries(themes).map(([themeName, themeProperties]) => (
                                <div>
                                  {post.category === themeProperties.title && (
                                    <div
                                      onClick={() => getUserID(post?.id)}
                                      className=" cursor-pointer w-[350px]"
                                    >
                                      {/* <p>{post.profile_name}</p> */}
                                      <GlobalCardEvents
                                        post={post}
                                        border={themeProperties.backgroundColor}
                                      />
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div
                              onClick={() => getUserID(post?.id)}
                              className=" cursor-pointer w-[350px]"
                            >
                              <GlobalCardEvents post={post} border={props.border} />
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500">No Events available in this category.</p>
                    );
                  })()}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default EventsCard;

// "use client";
// import { useCallback, useEffect, useRef, useState } from "react";
// import { GlobalCardEvents } from "@/globalComponents/globalCardEvents";
// import { getAllEvents, getEventsByCategory } from "@/services/eventsServices";
// import { getUserByEventId, getUserById } from "@/services/usersServices";
// import { themes } from "../../../../theme";
// import Link from "next/link";
// import { useRouter } from "next/navigation";
// import useAuth from "@/hook";
// import useProfile from "@/hook/profileData";
// import LoadingOverlay from "@/components/loadingOverlay";

// const EventsCard = (props: any) => {
//   const user = useAuth();
//   const [loading, setLoading] = useState<boolean>(true);

//   const [events, setEvents]: any = useState([]);

//   useEffect(() => {
//     const fetchAllServices = async () => {
//       setLoading(true);
//       if (props.themeProperties.title) {
//         const responsedan = await getEventsByCategory(
//           props.themeProperties.title === "My Feed"
//             ? "My Feed" // Pass "My Feed" as category_name
//             : props.themeProperties.title,
//           user?.userId // Add user ID as second parameter
//         );
//         setEvents(responsedan.events);
//         console.log({ responsedan });

//         setLoading(false);
//       }
//     };

//     fetchAllServices();
//   }, [props]);

//   return (
//     <>
//       {loading ? (
//         <LoadingOverlay isLoading={loading} />
//       ) : (
//         <div className="w-full mt-2 grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3">
//           {events.map((item: any, index: number) => (
//             <div key={index} className="mb-2 ">
//               {Object.entries(themes).map(([_, innerThemeProperties]) => (
//                 <div key={innerThemeProperties.title}>
//                   {(item.category === "Storytelling"
//                     ? "Literature"
//                     : item.category) === innerThemeProperties.title && (
//                     <Link href={`/profile/${item.user_id}?view=Services`}>
//                       <p>{item.name}</p>
//                       <GlobalCardEvents
//                         post={item}
//                         border={innerThemeProperties.backgroundColor}
//                       />
//                     </Link>
//                   )}
//                 </div>
//               ))}
//             </div>
//           ))}
//         </div>
//       )}
//     </>
//   );
// };

// export default EventsCard;
