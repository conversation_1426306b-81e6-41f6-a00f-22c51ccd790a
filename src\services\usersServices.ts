import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  collection,
  getDocs,
  getFirestore,
  arrayUnion,
  arrayRemove,
  writeBatch,
  Timestamp,
  deleteField,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";

import { query, where } from "firebase/firestore";
import { User } from "./UserInterface";
import { getAuth, sendEmailVerification, sendPasswordResetEmail } from "firebase/auth";
import { NotificationEvents, NotificationManager } from "./notificationService";
import { filterPostsByPublishingDate, GetPostDetailsByPostId } from "./postService";
import { GetOrderDetailsByUserId } from "./ordersServices";
import { ChatManager } from "./chatService";
import { PublishingDateFilter } from "./filtersServices";
import { MailServiceManager } from "./MailService";

interface GetFiltersInput {
  category?: string[];
  user_id?: string[];
  location?: string[];
  date_of_publishing?: PublishingDateFilter;
}

// **Create User Service**
export const createUser = async (userId: string, userData: any) => {
  try {
    const { db } = await initFirebase();

    const usersCollection = collection(db, "users");
    const userRef = doc(db, "users", userId);
    const resp = await setDoc(userRef, userData);
    return { success: true };
  } catch (error) {
    console.error("Error creating user:", error);
    return { success: false, error: "Server error" };
  }
};
// **Get All Users Service**

const userFilterWrapper = async ({
  filters,
  users,
}: {
  filters: GetFiltersInput;
  users: User[];
}): Promise<any> => {
  try {
    let finalResp: any[] = [];
    let filterApplied: boolean = false;

    if (filters?.date_of_publishing) {
      users = filterPostsByPublishingDate(users, filters?.date_of_publishing, "users") as User[];
    }

    if (filters?.category?.length || filters?.location?.length || filters?.user_id?.length) {
      filterApplied = true;
      for (let i = 0; i < users?.length; i++) {
        let current = users?.[i];

        // category filter
        if (filters?.category?.length) {
          if (filters?.category?.includes("Literature")) {
            filters.category = [...filters?.category, "Storytelling"];
          }
          let filterCat: string[] = filters.category;
          let userCat: string[] = current?.categories ?? [];

          let mp: { [k: string]: number } = {};

          filterCat?.forEach((curr) => (mp[curr] ? (mp[curr] += 1) : (mp[curr] = 1)));

          userCat?.forEach((curr) => (mp[curr] ? (mp[curr] += 1) : (mp[curr] = 1)));

          if (Object.values(mp).sort()?.[Object.values(mp)?.length - 1] > 1) {
            finalResp?.push(current);
          }
        }

        // location filter
        if (filters?.location?.length) {
          if (filters?.location?.includes(current?.location)) {
            finalResp?.push(current);
          }
        }

        // user_id
        if (filters?.user_id?.length) {

          // @ts-ignore
          if (current?.id && filters?.user_id?.includes(current?.id)) {
          console.log({current});
            finalResp?.push(current);
          }
        }
      }
    }

    if (!filterApplied) {
      finalResp = users;
    }

    return finalResp;
  } catch (error) {}
};

// check delete
export const getAllUsers = async (filters?: Omit<GetFiltersInput, "date_of_publishing">) => {
  try {
    const { db } = await initFirebase();
    const usersRef = collection(db, "users");
    const snapshot = await getDocs(usersRef);
    let usersList: any[] = snapshot.docs
      .map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as User
      )
      .filter((user: User) => user.isDeleted !== true);

      
    if (filters) {
      usersList = await userFilterWrapper({ filters, users: usersList });
    }

    return { success: true, users: usersList };
  } catch (error) {
    // console.error("Error fetching users:", error);
    return { success: false, error: "Server error" };
  }
};

// **Get User by ID Service**
//🔥
const getFollowersIds = async (userId: string): Promise<string[]> => {
  const { db } = await initFirebase();
  const usersCollection = collection(db, "users");

  try {
    const followersQuery = query(usersCollection, where("bookmarks", "array-contains", userId));
    const followersSnap = await getDocs(followersQuery);

    return followersSnap.docs.map((doc) => doc.id);
  } catch (error) {
    console.error("Error fetching followers:", error);
    return [];
  }
};
export const getUserById = async (
  userId: string
): Promise<{
  success: boolean;
  user: User;
}> => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);

    if (userSnap.exists()) {
      const userData = userSnap.data() as User;
      if (userData.currency) {
        localStorage.setItem("currency", JSON.stringify(userData.currency)); // Store user data
      } else {
        localStorage.setItem("currency", "gbp"); // Store user data
      }
      return {
        success: true,
        user: {
          ...userData,
          following: userData.bookmarks || [],
          followers: (await getFollowersIds(userId)) || [],
        } as User,
      };
    } else {
      return { success: false, user: {} as User };
    }
  } catch (error) {
    return { success: false, user: {} as User };
    // throw new Error("user cannot be fetched"); // check this
  }
};

// export const getUsersByIds = async (
//   userIds: string[]
// ): Promise<{
//   success: boolean;
//   users: Array<User & { posts: any }>; // Or `posts: string[]` if you only store IDs
// }> => {
//   console.log(userIds, "userIdsuserIdsuserIds");

//   try {
//     const usersData: any = [];

//     for (const userId of userIds) {
//       const userRef = doc(db, "users", userId);
//       const userSnap = await getDoc(userRef);

//       if (!userSnap.exists()) continue;

//       const userData = userSnap.data() as User;

//       // Fetch post details using post IDs
//       const postPromises = userData.posts.map(async (postId: string) => {
//         const postRef = doc(db, "posts", postId);
//         const postSnapshot = await getDoc(postRef);

//         return postSnapshot.exists()
//           ? { id: postSnapshot.id, ...postSnapshot.data() }
//           : null;
//       });

//       let posts = await Promise.all(postPromises);

//       // posts = posts.sort(
//       //   (a: any, b: any) => (b.added_at?.seconds || 0) - (a.added_at?.seconds || 0)
//       // );

//       usersData.push({
//          userSnap.id,
//         ...userData,
//         following: userData.bookmarks || [],
//         followers: (await getFollowersIds(userId)) || [],
//         posts, // Now properly typed
//       });
//     }

//     console.log({ usersData });

//     return { success: true, users: usersData };
//     // return { id: userDoc.id, ...userData, posts };
//   } catch (error) {
//     console.error("Error fetching users:", error);
//     return { success: false, users: [] };
//   }
// };

export const getUsersByIds = async (
  userIds: string[],
  filters?: Omit<GetFiltersInput, "date_of_publishing">
): Promise<{
  success: boolean;
  users: Array<User & { posts: any[]; following: string[]; followers: string[] }>;
}> => {
  try {
    let usersData: any[] = [];
    const { db } = await initFirebase();

    for (const userId of userIds) {
      const userRef = doc(db, "users", userId);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) continue;

      const userData = userSnap.data() as User;

      // Fetch post details using post IDs
      if (userData?.posts && userData?.posts.length > 0) {
        const postPromises = userData?.posts?.map(async (postId: string) => {
          const postRef = doc(db, "posts", postId);
          const postSnapshot = await getDoc(postRef);

          return postSnapshot.exists() ? { id: postSnapshot.id, ...postSnapshot.data() } : null;
        });

        const posts = (await Promise.all(postPromises)).filter(Boolean);

        usersData.push({
          ...userData,
          // following: userData.bookmarks || [],
          // followers: (await getFollowersIds(userId)) || [],
          id: userSnap.id,
          posts,
        });
      }
    }
    if (filters) {
      usersData = await userFilterWrapper({ filters, users: usersData });
    }    
    

    return { success: true, users: usersData };
  } catch (error) {
    console.error("Error fetching users:", error);
    return { success: false, users: [] };
  }
};

// 📌 Get starred posts for a specific user by their ID

// get user by event id
export const getUserByPostId = async (eventId: string) => {
  try {
    const { db } = await initFirebase();

    const usersCollection = collection(db, "users");
    // Query the users collection to find users whose events array contains the given eventId
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("posts", "array-contains", eventId));

    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      // If any user document matches the eventId in their events array, return the user data
      const users = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return { success: true, users }; // Return all matching users
    } else {
      return { success: false, error: "No users found for this event" };
    }
  } catch (error) {
    // console.error("Error fetching user:", error);
    return { success: false, error: "Server error" };
  }
};
// get user by event id
export const getUserByEventId = async (eventId: string) => {
  try {
    const { db } = await initFirebase();
    // Query the users collection to find users whose events array contains the given eventId
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("events", "array-contains", eventId));

    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      // If any user document matches the eventId in their events array, return the user data
      const users = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return { success: true, users }; // Return all matching users
    } else {
      return { success: false, error: "No users found for this event" };
    }
  } catch (error) {
    // console.error("Error fetching user:", error);
    return { success: false, error: "Server error" };
  }
};

// get user by services id

export const getUserByServicesId = async (eventId: string) => {
  try {
    const { db } = await initFirebase();

    // Query the users collection to find users whose events array contains the given eventId
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("services", "array-contains", eventId));

    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      // If any user document matches the eventId in their events array, return the user data
      const users = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return { success: true, users }; // Return all matching users
    } else {
      return { success: false, error: "No users found for this event" };
    }
  } catch (error) {
    // console.error("Error fetching user:", error);
    return { success: false, error: "Server error" };
  }
};

// **Update User Service**
export const updateUser = async (userId: string | undefined, updatedData: any) => {
  try {
    if (!userId) {
      console.error("Error updating user: userId is undefined");
      return { success: false, error: "User ID is required" };
    }

    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);
    if (userSnap.exists()) {
      const current_profile_name = userSnap.data().profile_name;

      console.log({ userId, updatedData, current_profile_name });
      if (Object.keys(updatedData).includes("profile_name") && updatedData?.["profile_name"]) {
        const usersRef = collection(db, "users");
        const q = query(usersRef, where("profile_name", "==", updatedData?.["profile_name"]));
        const snapshot = await getDocs(q);
        const data = snapshot?.docs?.[0]?.data();

        if (data?.id && data?.id !== userId) {
          return { success: false, error: "user name already exists!" };
        }
      }

      await updateDoc(userRef, updatedData);
      return { success: true };
    }

    return { success: false };
  } catch (error) {
    // console.error("Error updating user:", error);
    return { success: false, error: "Server error" };
  }
};

// **Delete User Service** dont use this
// export const deleteUser = async (userId: string) => {
//   try {
//     const { db } = await initFirebase();
//     const userRef = doc(db, "users", userId);
//     await deleteDoc(userRef);
//     return { success: true };
//   } catch (error) {
//     // console.error("Error deleting user:", error);
//     return { success: false, error: "Server error" };
//   }
// };

// **Add a Bookmark to a User**
export const addBookmark = async (userId: string, bookmarkId: string) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);
    if (userSnap.exists()) {
      const currentBookmarks = userSnap.data().bookmarks || [];
      if (!currentBookmarks.includes(bookmarkId)) {
        currentBookmarks.push(bookmarkId);
        await updateDoc(userRef, { bookmarks: currentBookmarks });
        return { success: true };
      }
    }
    return { success: false, error: "User not found" };
  } catch (error) {
    // console.error("Error adding bookmark:", error);
    return { success: false, error: "Server error" };
  }
};

// **Remove a Bookmark from a User**
export const removeBookmark = async (userId: string, bookmarkId: string) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);
    if (userSnap.exists()) {
      const currentBookmarks = userSnap.data().bookmarks || [];
      const updatedBookmarks = currentBookmarks.filter((id: string) => id !== bookmarkId);
      await updateDoc(userRef, { bookmarks: updatedBookmarks });
      return { success: true };
    }
    return { success: false, error: "User not found" };
  } catch (error) {
    // console.error("Error removing bookmark:", error);
    return { success: false, error: "Server error" };
  }
};

// **Add an Event to User's Events List**
export const addEventToUser = async (userId: string, eventId: string) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);
    if (userSnap.exists()) {
      const currentEvents = userSnap.data().events || [];
      if (!currentEvents.includes(eventId)) {
        currentEvents.push(eventId);
        await updateDoc(userRef, { events: currentEvents });
        return { success: true };
      }
    }
    return { success: false, error: "User not found" };
  } catch (error) {
    // console.error("Error adding event:", error);
    return { success: false, error: "Server error" };
  }
};

// **Remove an Event from User's Events List**
export const removeEventFromUser = async (userId: string, eventId: string) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);
    if (userSnap.exists()) {
      const currentEvents = userSnap.data().events || [];
      const updatedEvents = currentEvents.filter((id: string) => id !== eventId);
      await updateDoc(userRef, { events: updatedEvents });
      return { success: true };
    }
    return { success: false, error: "User not found" };
  } catch (error) {
    // console.error("Error removing event:", error);
    return { success: false, error: "Server error" };
  }
};

// add star post

export const toggleStarredPost = async (userId: string, postId: string, isStarred: boolean) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user?.uid) {
      return { success: false };
    }
    const userRef = doc(db, "users", userId);

    await updateDoc(userRef, {
      starredPosts: isStarred ? arrayRemove(postId) : arrayUnion(postId),
    });

    // notification
    if (
      !isStarred ||
      user.uid !== userId // no notification for own reaction
    ) {
      const post_details = await GetPostDetailsByPostId({ post_id: postId });
      if (!post_details?.user_id || user?.uid === post_details?.user_id) {
        return { success: true };
      }
      NotificationManager.getInstance().CreateNotification({
        payload: {
          src_id: user?.uid,
          dest_id: post_details?.user_id,
          event: NotificationEvents.REACTION,
          post_id: postId,
          post_url: post_details?.postFile,
          thumbnail_url: post_details?.thumbnailUrl,
        },
      });
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: "Server error" };
  }
};

// bookmark
// post_bookmarked
export const toggleBookMarks = async (userId: string, postId: string, isStarred: boolean) => {
  try {
    const { db } = await initFirebase();
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user?.uid) {
      return { success: false };
    }
    const userRef = doc(db, "users", userId);
    // console.log({ userId, postId, isStarred });

    await updateDoc(userRef, {
      post_bookmarked: isStarred ? arrayRemove(postId) : arrayUnion(postId),
    });

    if (!isStarred) {
      const post_details = await GetPostDetailsByPostId({ post_id: postId });
      if (!post_details?.user_id || user?.uid === post_details?.user_id) {
        return { success: true };
      }

      NotificationManager.getInstance().CreateNotification({
        payload: {
          src_id: user?.uid,
          dest_id: post_details?.user_id,
          event: NotificationEvents.BOOKMARK,
          post_id: postId,
          post_url: post_details?.postFile,
          thumbnail_url: post_details?.thumbnailUrl,
        },
      });
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: "Server error" };
  }
};
const USER_COLLECTION = "users";
const POST_COLLECTION = "posts";
const SERVICES_COLLECTION = "services";
const EVENT_COLLECTION = "events";

export const getUserIdByPostServiceEvent = async (id: string) => {
  try {
    const { db } = await initFirebase();

    const userRef = collection(db, USER_COLLECTION);

    // Check which type of ID this is
    let q = query(userRef, where("postIds", "array-contains", id));
    let querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      return {
        type: "post",
        user: querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() })),
      };
    }

    q = query(userRef, where("eventIds", "array-contains", id));
    querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      return {
        type: "event",
        user: querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() })),
      };
    }

    q = query(userRef, where("serviceIds", "array-contains", id));
    querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      return {
        type: "service",
        user: querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() })),
      };
    }

    return { message: "ID not found in any collection" };
  } catch (error) {
    console.log({ error });
    throw error;
  }
};

/**
 *
 *  await UpdateSocials({
      user_id: "someUserId",
      type: "instagramLink",
      url: "https://instagram.com/example",
    });
 *
 */

export const UpdateSocials = async ({
  user_id,
  type,
  url,
}: {
  user_id: string;
  type: "facebookLink" | "instagramLink" | "twitterLink" | "websiteLink" | "youtubeLink";
  url: string;
}) => {
  try {
    const { db } = await initFirebase();

    const userRef = doc(db, USER_COLLECTION, user_id);

    const resp = await updateDoc(userRef, {
      [type]: url,
    });

    // console.log(`${type} updated successfully`);

    return "success";
  } catch (error) {
    console.log({ error });
    throw error;
  }
};

// export const getUsersByCategory = async (category: any) => {
//   try {
//     const usersRef = collection(db, "users");
//     const q = query(usersRef, where("categories", "array-contains", category));
//     const querySnapshot = await getDocs(q);

//     const users = querySnapshot.docs.map((doc) => ({
//       id: doc.id,
//       ...doc.data(),
//     }));

//     return users;
//   } catch (error) {
//     console.error("Error fetching users:", error);
//     return [];
//   }
// };

export const getUsersByCategory = async (category: any) => {
  try {
    const { db } = await initFirebase();
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("categories", "array-contains", category));
    const querySnapshot = await getDocs(q);

    const users = querySnapshot.docs
      .map((doc) => {
        const userData = doc.data();
        return userData.posts && userData.posts.length > 0 && userData?.isDeleted !== true // Ensure user has posts
          ? { id: doc.id, ...userData }
          : null;
      })
      .filter((user) => user !== null); // Remove users with no posts

    return users;
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
};

///  delete

// user
const updateUserDeleteFlag = async ({
  user_id,
  flag,
  comment,
}: {
  user_id: string;
  flag: boolean;
  comment: string;
}) => {
  try {
    const { db } = await initFirebase();
    const oldRef = doc(db, "users", user_id);
    const snapshot = await getDoc(oldRef);

    if (snapshot.exists()) {
      const data = snapshot.data();

      // Move to deleted_users collection
      const newRef = doc(db, "deleted_users", user_id);

      await setDoc(
        newRef,
        {
          ...data,
          delete_reason: comment,
          user_id_rem: user_id,
          isDeleted: true,
          id: deleteField(),
        },
        { merge: true }
      );

      // Delete from original collection
      await deleteDoc(oldRef);
    }

    return { success: true };
  } catch (error) {
    console.log({ error });
    throw new Error("user_delete_flag_failed");
  }
};
//post
export const updatePostDeleteFlag = async ({
  user_id,
  flag,
}: {
  user_id: string;
  flag: boolean;
}) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", user_id);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      throw new Error("User not found");
    }

    const userData = userSnap.data();
    const postIds: string[] = userData.posts || []; // Get the user's posts

    if (postIds.length === 0) {
      return { success: true, message: "No posts to update" };
    }

    for (const postId of postIds) {
      const postRef = doc(db, "posts", postId);
      const postSnap = await getDoc(postRef);

      if (!postSnap.exists()) continue;

      const postData = postSnap.data();

      // Move to deleted_posts collection
      await setDoc(doc(db, "deleted_posts", postId), {
        ...postData,
        deleted: true,
        user_id_rem: user_id,
        post_id_rem: postId,
      });

      // Delete original post
      await deleteDoc(postRef);
    }

    // // Use batch update for efficiency
    // const batch = writeBatch(db);

    // postIds.forEach((postId) => {
    //   const postRef = doc(db, "posts", postId);
    //   batch.update(postRef, {
    //     user_id_rem: user_id, // optional, just to keep a reference
    //     post_id_rem: postId,
    //     user_id: deleteField(), // remove original field so it no longer matches query
    //     id: deleteField(), // optional: remove the 'id' field
    //     deleted: flag,
    //   });
    // });

    // await batch.commit();

    return { success: true, updatedPosts: postIds };
  } catch (error) {
    console.error("Error updating post delete flag:", error);
    throw new Error("post_delete_flag_failed");
  }
};
// events
const updateEventsDeleteFlag = async ({ user_id, flag }: { user_id: string; flag: boolean }) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", user_id);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      throw new Error("User not found");
    }

    const userData = userSnap.data();
    const eventIds: string[] = userData.events || [];

    if (eventIds.length === 0) {
      return { success: true, message: "No events to update" };
    }

    for (const eventId of eventIds) {
      const eventRef = doc(db, "events", eventId);
      const eventSnap = await getDoc(eventRef);

      if (!eventSnap.exists()) continue;

      const eventData = eventSnap.data();

      // Move to deleted_posts collection
      await setDoc(doc(db, "deleted_events", eventId), {
        ...eventData,
        deleted: true,
        user_id_rem: user_id,
        event_id_rem: eventId,
      });

      // Delete original post
      await deleteDoc(eventRef);
    }

    // Use batch update for efficiency
    // const batch = writeBatch(db);

    // eventIds.forEach((eventId) => {
    //   const eventRef = doc(db, "events", eventId);
    //   batch.delete(eventRef);
    //   //   batch.update(eventRef, {
    //   // user_id_rem: user_id,  // optional, just to keep a reference
    //   // event_id_rem: eventId,
    //   // user_id: deleteField(),   // remove original field so it no longer matches query
    //   // id: deleteField(),        // optional: remove the 'id' field
    //   //      deleted: flag
    //   //    });
    // });

    // await batch.commit();

    return { success: true, updatedEvents: eventIds };
  } catch (error) {
    console.error("Error updating event delete flag:", error);
    throw new Error("event_delete_flag_failed");
  }
};
// services
const updateServicesDeleteFlag = async ({ user_id, flag }: { user_id: string; flag: boolean }) => {
  try {
    const { db } = await initFirebase();

    const userRef = doc(db, "users", user_id);
    const userSnap = await getDoc(userRef);
    await updateDoc(userRef, {
      services: [],
    });
    if (!userSnap.exists()) {
      throw new Error("User not found");
    }

    const userData = userSnap.data();
    const serviceIds: string[] = userData.services || [];

    if (serviceIds.length === 0) {
      return { success: true, message: "No services to update" };
    }

    for (const serviceId of serviceIds) {
      const serviceRef = doc(db, "services", serviceId);
      const serviceSnap = await getDoc(serviceRef);

      if (!serviceSnap.exists()) continue;

      const eventData = serviceSnap.data();

      // Move to deleted_posts collection
      await setDoc(doc(db, "deleted_services", serviceId), {
        ...eventData,
        deleted: true,
        user_id_rem: user_id,
        service_id_rem: serviceId,
      });

      // Delete original post
      await deleteDoc(serviceRef);
    }

    // Use batch update for efficiency
    // const batch = writeBatch(db);

    // serviceIds.forEach((serviceId) => {
    //   console.log({ serviceId });

    //   const serviceRef = doc(db, "services", serviceId);
    //   batch.delete(serviceRef);
    //   //   batch.update(eventRef, {
    //   //     //  deleted: flag
    //   //         user_id_rem: user_id,  // optional, just to keep a reference
    //   // service_id_rem: serviceId,
    //   // user_id: deleteField(),   // remove original field so it no longer matches query
    //   // id: deleteField(),        // optional: remove the 'id' field
    //   //      deleted: flag

    //   //     });
    // });

    // await batch.commit();

    return { success: true, updatedServices: serviceIds };
  } catch (error) {
    console.error("Error updating service delete flag:", error);
    throw new Error("service_delete_flag_failed");
  }
};
// orders
const updateOrdersDeleteFlag = async ({ user_id, flag }: { user_id: string; flag: boolean }) => {
  try {
    const { db } = await initFirebase();
    const ordersRef = collection(db, "orders");
    const q = query(ordersRef, where("userProfileId", "==", user_id));

    const snapshot = await getDocs(q);
    if (snapshot.empty) {
      return { success: true, message: "No orders to update" };
    }

    // Use batch update for efficiency
    const batch = writeBatch(db);

    snapshot.docs.forEach((doc) => {
      batch.update(doc.ref, { deleted: flag });
    });

    await batch.commit(); // Commit all updates at once

    return { success: true, updatedOrders: snapshot.docs.length };
  } catch (error) {
    console.error("Error updating orders delete flag:", error);
    throw new Error("orders_delete_flag_failed");
  }
};
// auth-bridge
const updateAuthBridgeDeleteFlag = async ({ user_id }: { user_id: string }) => {
  try {
    const { db } = await initFirebase();
    const authBridgeRef = collection(db, "auth_bridge");
    const q = query(authBridgeRef, where("user_id", "==", user_id));

    const snapshot = await getDocs(q);
    if (snapshot.empty) {
      return { success: true, message: "No matching auth records found" };
    }
    // Delete all matching documents
    const deletePromises = snapshot.docs.map((doc) => deleteDoc(doc.ref));
    await Promise.all(deletePromises);

    return { success: true, deletedCount: snapshot.docs.length };
  } catch (error) {
    console.error("Error deleting auth records:", error);
    throw new Error("auth_delete_failed");
  }
};
// followers / followings
const updateFollowDeleteFlag = async ({ user_id }: { user_id: string }) => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", user_id);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      throw new Error("User not found");
    }

    const userData = userSnap.data();
    const followers: string[] = userData.followers || [];
    const following: string[] = userData.bookmarks || [];

    // Remove user_id from each follower's "following" array
    const removeFromFollowingPromises = followers.map(async (followerId) => {
      const followerRef = doc(db, "users", followerId);
      return updateDoc(followerRef, { following: arrayRemove(user_id) });
    });

    // Remove user_id from each following user's "followers" array
    const removeFromFollowersPromises = following.map(async (followingId) => {
      const followingRef = doc(db, "users", followingId);
      return updateDoc(followingRef, { followers: arrayRemove(user_id) });
    });

    // Execute all updates in parallel
    await Promise.all([...removeFromFollowingPromises, ...removeFromFollowersPromises]);

    return {
      success: true,
      message: "User removed from all follow relationships",
    };
  } catch (error) {
    console.error("Error updating follow relationships:", error);
    throw new Error("follow_delete_failed");
  }
};
// comments
const updateCommentsDeleteFlag = async ({ user_id, flag }: { user_id: string; flag: boolean }) => {
  try {
    const { db } = await initFirebase();
    const commentsRef = collection(db, "comments");
    const q = query(commentsRef, where("user_id", "==", user_id));
    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      // console.log("No comments found for user:", user_id);
      return;
    }

    const batch = writeBatch(db);
    snapshot.docs.forEach((doc) => {
      batch.update(doc.ref, { hidden: flag });
    });

    await batch.commit();
    // console.log(`Updated ${snapshot.size} comments`);
  } catch (error) {
    console.error("Error updating comments:", error);
    throw new Error("comments_update_del_flag_failed");
  }
};

export const deleteUserDetails = async ({
  user_id,
  comment,
}: {
  user_id: string;
  comment: string;
}) => {
  try {
    const { auth } = await initFirebase();
    const user = auth.currentUser;

    if (!user) {
      throw new Error("user not detected please login again ");
    }
    let _user_id = user.uid;

    if (user_id !== _user_id) {
      throw new Error("wrong user id sent");
    }

    const results = await Promise.allSettled([
      updateUserDeleteFlag({ user_id, flag: true, comment }),
      updatePostDeleteFlag({ user_id, flag: true }),
      updateEventsDeleteFlag({ user_id, flag: true }),
      updateServicesDeleteFlag({ user_id, flag: true }),
      // updateOrdersDeleteFlag({ user_id, flag: true }),
      // updateAuthBridgeDeleteFlag({ user_id }),
      updateFollowDeleteFlag({ user_id }), /// ℹ️ Need to update
      updateCommentsDeleteFlag({ user_id, flag: true }),
    ]);

    // Log the results for debugging
    results.forEach((result, index) => {
      if (result.status === "rejected") {
        // console.error(Operation ${index + 1} failed:, result.reason);
      }
    });

    const resp = await user?.delete();
    console.log({ resp });

       await MailServiceManager.getInstance()?.sendMail({
                  toMail: "<EMAIL>",
                  type:"profile_deleted",
                  message:{
                   profileId:user_id,
                   reason:comment
                  }
                });     
    

    return {
      success: true,
      message: "User deletion process completed",
      results,
    };
  } catch (error) {
    console.log({ error });
    throw new Error("delete user details failed");
  }
};

export const calculateProfileComplete = async ({ user_id }: { user_id: string }) => {
  try {
    //     profile_name 10 %
    // about_me 10 %
    // personal_moto 10 %
    // avatar 20 %
    // categories 30 % // ie added any categories
    // posts 20% // ie added any post
    const { db } = await initFirebase();
    const userRef = doc(db, "users", user_id);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      throw new Error("User not found");
    }

    const userData = userSnap.data();
    let completionPercentage = 0;

    // Check each field and add corresponding percentage
    if (userData?.profile_name) completionPercentage += 10;
    if (userData?.about_me) completionPercentage += 10;
    if (userData?.personal_moto) completionPercentage += 10;
    if (userData?.avatar) completionPercentage += 20;
    if (userData?.categories && userData?.categories?.length > 0) completionPercentage += 30;
    if (userData?.posts && userData?.posts?.length > 0) completionPercentage += 20;

    return { success: true, completionPercentage };
  } catch (error) {
    console.log({ error });
  }
};

// export const getUsersByCategoryWithPost = async (category: any) => {
//   try {
//     const {db} =  await initFirebase();

//     const usersRef = collection(db, "users");
//     const q = query(usersRef, where("categories", "array-contains", category));
//     const querySnapshot = await getDocs(q);

//     const usersWithPosts = await Promise.all(
//       querySnapshot.docs.map(async (userDoc) => {
//         const userData = userDoc.data();

//         if (!userData.posts || userData.posts.length === 0) {
//           return { id: userDoc.id, ...userData, posts: [] };
//         }

//         // Fetch post details using post IDs
//         const postPromises = userData.posts.map(async (postId: string) => {
//           const postRef = doc(db, "posts", postId);
//           const postSnapshot = await getDoc(postRef);
//           return postSnapshot.exists()
//             ? { id: postSnapshot.id, ...postSnapshot.data() }
//             : null;
//         });

//         let posts = (await Promise.all(postPromises)).filter(
//           (post) => post !== null && post.category === category
//         );

//         posts = posts.sort(
//           (a, b) => (b.added_at?.seconds || 0) - (a.added_at?.seconds || 0)
//         );
//         return { id: userDoc.id, ...userData, posts };
//       })
//     );

//     const users = usersWithPosts
//       .map((doc) => {
//         return doc.posts && doc.posts.length > 0 // Ensure user has posts
//           ? { ...doc }
//           : null;
//       })
//       .filter((user) => user !== null); // Remove users with no posts

//     return users;
//   } catch (error) {
//     console.error("Error fetching users and posts:", error);
//     return [];
//   }
// };

// email verification

export const getUsersByCategoryWithPost = async (categories: string[],
  filters?: Omit<GetFiltersInput, "date_of_publishing">
) => {
  try {
    const { db } = await initFirebase();

    const usersRef = collection(db, "users");

    // Since Firestore doesn't support "array-contains-any" in combination with post filtering later,
    // we'll query users whose categories contain ANY of the specified categories
    const q = query(usersRef, where("categories", "array-contains-any", categories));
    const querySnapshot = await getDocs(q);

    const usersWithPosts = await Promise.all(
      querySnapshot.docs.map(async (userDoc) => {
        const userData = userDoc.data();

        if (!userData.posts || userData.posts.length === 0) {
          return { id: userDoc.id, ...userData, posts: [] };
        }

        // Fetch all post data
        const postPromises = userData.posts.map(async (postId: string) => {
          const postRef = doc(db, "posts", postId);
          const postSnapshot = await getDoc(postRef);
          return postSnapshot.exists() ? { id: postSnapshot.id, ...postSnapshot.data() } : null;
        });

        let posts = (await Promise.all(postPromises)).filter(
          (post) => post !== null && categories.includes(post.category)
        );

        posts = posts.sort((a, b) => (b.added_at?.seconds || 0) - (a.added_at?.seconds || 0));

        return posts.length > 0 ? { id: userDoc.id, ...userData, posts } : null;
      })
    );

    let resp:any = usersWithPosts.filter((user) => user !== null);
    
    if (filters) {
      resp = await userFilterWrapper({ filters, users: resp });
    }

    
    return resp;
  } catch (error) {
    console.error("Error fetching users and posts:", error);
    return [];
  }
};

export const sendVerificationEmail = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    return { success: false, error: "No user is signed in" };
  }

  try {
    await sendEmailVerification(user);
    return { success: true, message: "Verification email sent successfully" };
  } catch (error) {
    console.error("Error sending verification email:", error);
    return { success: false, error: "Failed to send verification email" };
  }
};
export const sendResetPassword = async (email: string) => {
  const auth = getAuth();
  const user = auth.currentUser;

  // if (!user) {
  //   return { success: false, error: "No user is signed in" };
  // }
  if (!email) {
    return { success: false, error: " Entered email " };
  }

  try {
    await sendPasswordResetEmail(auth, email);
    return { success: true, message: "Reset email sent successfully" };
  } catch (error) {
    console.error("Error sending Reset email:", error);
    return { success: false, error: "Failed to send Reset email" };
  }
};

export const isEmailVerified = () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    return { success: false, error: "No user is signed in" };
  }

  return { success: true, verified: user.emailVerified };
};

export const refreshUser = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    return { success: false, error: "No user is signed in" };
  }

  try {
    await user.reload(); // Reload user info from Firebase
    return { success: true, verified: user.emailVerified };
  } catch (error) {
    console.error("Error refreshing user:", error);
    return { success: false, error: "Failed to refresh user status" };
  }
};

// don't apply this function untill you want to delete the data based on created date
export const deleteServicesByDate = async () => {
  try {
    // Create date boundaries for December 24, 2024 (UTC+5:30)
    const startDate = new Date("December 24, 2024 00:00:00 GMT+0530");
    const endDate = new Date("December 25, 2024 00:00:00 GMT+0530");

    // Create the query to find all documents created on December 24
    const { db } = await initFirebase();
    const servicesRef = collection(db, "services");
    const q = query(
      servicesRef,
      where("created_at", ">=", Timestamp.fromDate(startDate)),
      where("created_at", "<", Timestamp.fromDate(endDate))
    );

    // Execute the query
    const querySnapshot = await getDocs(q);

    // console.log(`Found ${querySnapshot.size} documents to delete`);

    // Delete each document
    const deletePromises: any[] = [];
    querySnapshot.forEach((doc) => {
      // console.log(`Deleting service: ${doc.data().title} (ID: ${doc.id})`);
      deletePromises.push(deleteDoc(doc.ref));
    });

    // Wait for all deletions to complete
    await Promise.all(deletePromises);

    console.log("All services on December 24, 2024 have been deleted successfully");
  } catch (error) {
    console.error("Error deleting services:", error);
  }
};

const adjectivesBase = [
  "cool",
  "fast",
  "blue",
  "lucky",
  "smart",
  "brave",
  "bright",
  "calm",
  "clean",
  "clever",
  "dark",
  "eager",
  "fancy",
  "fresh",
  "funny",
  "gentle",
  "glad",
  "happy",
  "jolly",
  "kind",
  "loud",
  "modern",
  "nice",
  "polite",
  "proud",
  "quick",
  "rare",
  "rich",
  "shy",
  "silly",
  "strong",
  "sweet",
  "tall",
  "tiny",
  "warm",
  "wild",
  "wise",
  "young",
  "zany",
  "bold",
];

const nounsBase = [
  "lion",
  "tiger",
  "panda",
  "eagle",
  "whale",
  "zebra",
  "monkey",
  "fox",
  "wolf",
  "bear",
  "shark",
  "rhino",
  "leopard",
  "giraffe",
  "rabbit",
  "swan",
  "owl",
  "bat",
  "camel",
  "dog",
  "cat",
  "mouse",
  "deer",
  "frog",
  "goat",
  "horse",
  "pig",
  "rat",
  "seal",
  "yak",
  "bee",
  "duck",
  "crab",
  "hen",
  "mole",
  "ant",
  "ape",
  "clam",
  "dove",
  "moth",
];

const checkUserNameExists = async (userName: string): Promise<boolean> => {
  const { db } = await initFirebase();
  const usersRef = collection(db, "users");
  const q = query(usersRef, where("profile_name", "==", userName));
  const snapshot = await getDocs(q);

  return !snapshot.empty;
};

function expandList(base: string[], count: number) {
  const result = [];
  const repeats = Math.ceil(count / base.length);
  for (let i = 0; i < repeats; i++) {
    for (const word of base) {
      if (result.length >= count) break;
      result.push(`${word}${i}`);
    }
  }
  return result;
}

// Final lists
const adjectives = expandList(adjectivesBase, 1500);
const nouns = expandList(nounsBase, 1500);

function generateUsername() {
  const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const number = Math.floor(Math.random() * 10000); // optional
  return `${adj}${noun}${number}`;
}

export const getUniqueUserName = async (user_name?: string): Promise<string> => {
  if (user_name) {
    const exists = await checkUserNameExists(user_name);
    if (!exists) return user_name;
  }

  let uniqueName: string;
  let attempts = 0;

  do {
    uniqueName = generateUsername();
    attempts++;
    if (attempts > 10) throw new Error("Too many attempts to generate a unique username");
  } while (await checkUserNameExists(uniqueName));

  return uniqueName;
};

// update script
// export const updateEmptyUserNames = async () => {
//   const { db } = await initFirebase();

//   // 1. Get all users with profile_name "" or "Profile Name"
//   const usersRef = collection(db, "users");
//   const q = query(usersRef, where("profile_name", "in", ["", "Profile Name"]));
//   const snapshot = await getDocs(q);

//   console.log(`Found ${snapshot.size} users to update.`);

//   for (const userDoc of snapshot.docs) {
//     const userId = userDoc.id;

//     // // 2. Generate a unique user_name
//     const uniqueUserName = await getUniqueUserName();

//     // // // 3. Update the profile_name
//     // await updateDoc(doc(db, "users", userId), {
//     //   profile_name: uniqueUserName,
//     // });

//     console.log(`✅ Updated user ${userId} to profile_name: ${uniqueUserName}`);
//   }
// };

export const GetSidebarCount = async () => {
  try {
    // const {auth} = await initFirebase();

    const user_id = // "W5437xR475cbUigqs5BlqWStrZw1"
      JSON?.parse(localStorage.getItem("user") ?? "")?.uid;

    if (!user_id)
      return {
        basketCount: 0,
        chatCount: 0,
        notificationCount: 0,
      };

    // Execute all in parallel
    const [orderRes, chatRes, notifCount] = await Promise.all([
      GetOrderDetailsByUserId({ userId: user_id }),
      ChatManager.getInstance().GetUserChatSummaries({ user_id }),
      NotificationManager.getInstance().UserUnreadNotificationCount({ user_id }),
    ]);

    return {
      basketCount: orderRes.basket?.length,
      myOrdersCount: orderRes?.my_orders?.length + orderRes?.received_orders?.length,
      chatCount: chatRes.totalUnreadCount,
      notificationCount: notifCount,
    };
  } catch (error) {
    console.log({ error });
    return {
      basketCount: 0,
      chatCount: 0,
      notificationCount: 0,
    };
  }
};

export const getUserIdByProfileName = async (profile_name: string): Promise<string | null> => {
  try {
    const { db } = await initFirebase();

    const q = query(collection(db, "users"), where("profile_name", "==", profile_name));

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.warn(`No user found with profile_name = ${profile_name}`);
      return null;
    }

    const userDoc = querySnapshot.docs[0];
    const userData = userDoc.data();

    return userData.id ?? userDoc.id; // prefer id field, fallback to Firestore doc ID
  } catch (error) {
    console.error("Error fetching user by profile_name:", error);
    return null;
  }
};

export const getProfileNameByUserId = async (user_id: string): Promise<string | null> => {
  try {
    const { db } = await initFirebase();

    const q = query(collection(db, "users"), where("id", "==", user_id));

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.warn(`No user found with id = ${user_id}`);
      return null;
    }

    const userDoc = querySnapshot.docs[0];
    const userData = userDoc.data();

    return userData.profile_name;
  } catch (error) {
    console.error("Error fetching user by profile_name:", error);
    return null;
  }
};

/**
 * Check if the logged-in user has a Stripe ID
 * Returns user data with stripe_id status
 */
export const checkUserStripeId = async (
  userId: string
): Promise<{
  success: boolean;
  hasStripeId: boolean;
  stripeId?: string;
  user?: User;
  error?: string;
}> => {
  try {
    const { db } = await initFirebase();
    const userRef = doc(db, "users", userId);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return {
        success: false,
        hasStripeId: false,
        error: "User not found",
      };
    }

    const userData = userSnap.data() as User;
    const hasStripeId = Boolean(userData.stripe_id);

    return {
      success: true,
      hasStripeId,
      stripeId: userData.stripe_id,
      user: userData,
    };
  } catch (error) {
    console.error("Error checking user Stripe ID:", error);
    return {
      success: false,
      hasStripeId: false,
      error: "Failed to check Stripe ID",
    };
  }
};


export const GetUserStripeId = async (uid: string): Promise<string | null> => {
  try {
    const { db } = await initFirebase();

    const userRef = doc(db, "users", uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return null;
    }

    const userData = userSnap?.data() ?? {} ;
    return userData.stripe_id ?? null;
  } catch (error) {
    console.error("GetUserStripeId failed:", error);
    throw new Error("GetUserStripeId failed");
  }
};
export const GetUserInfo = async (uid: string) => {
  try {
    const { db } = await initFirebase();

    const userRef = doc(db, "users", uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return null;
    }

    const userData = userSnap?.data() ?? {} ;
    return userData ?? {};
  } catch (error) {
    console.error("GetUserStripeId failed:", error);
    throw new Error("GetUserStripeId failed");
  }
};
